import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from './ui/dialog';
import { Button } from './ui/button';
import { AlertTriangle, Trash2, Power } from 'lucide-react';

interface SmartDeleteDialogProps {
  open: boolean;
  onClose: () => void;
  onDelete: () => void;
  onDisable?: () => void;
  title: string;
  description?: string;
  itemName: string;
  canDelete: boolean;
  hasOrders?: boolean;
}

const SmartDeleteDialog = ({ 
  open, 
  onClose, 
  onDelete, 
  onDisable,
  title, 
  description, 
  itemName,
  canDelete,
  hasOrders = false
}: SmartDeleteDialogProps) => {
  
  const handleDelete = () => {
    onDelete();
    onClose();
  };

  const handleDisable = () => {
    if (onDisable) {
      onDisable();
    }
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {canDelete ? (
              <Trash2 className="h-5 w-5 text-red-500" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-orange-500" />
            )}
            {title}
          </DialogTitle>
          <DialogDescription className="space-y-3">
            {canDelete ? (
              <div>
                <p>{description || `Yakin ingin menghapus "${itemName}"?`}</p>
                <p className="text-xs text-muted-foreground mt-2">
                  ⚠️ Aksi ini tidak dapat dibatalkan
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                  <p className="text-orange-800 font-medium">
                    Menu "{itemName}" tidak dapat dihapus
                  </p>
                  <p className="text-orange-700 text-sm mt-1">
                    {hasOrders 
                      ? "Menu ini sudah pernah dipesan oleh pelanggan" 
                      : "Menu ini masih digunakan dalam sistem"
                    }
                  </p>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-blue-800 font-medium">Alternatif yang disarankan:</p>
                  <p className="text-blue-700 text-sm mt-1">
                    Nonaktifkan menu ini agar tidak tampil untuk pelanggan baru, 
                    tetapi data historis tetap terjaga.
                  </p>
                </div>
              </div>
            )}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onClose}>
            Batal
          </Button>
          
          {canDelete ? (
            <Button type="button" variant="destructive" onClick={handleDelete}>
              <Trash2 className="h-4 w-4 mr-2" />
              Hapus Permanen
            </Button>
          ) : (
            onDisable && (
              <Button type="button" variant="secondary" onClick={handleDisable}>
                <Power className="h-4 w-4 mr-2" />
                Nonaktifkan Menu
              </Button>
            )
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SmartDeleteDialog;
