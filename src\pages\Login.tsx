
import { useState, useEffect } from 'react';
import { Eye, EyeOff, Lock, User, Store } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { UserRole } from '../types/pos';

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      const currentPath = window.location.pathname;
      if ((user.role === UserRole.ADMIN || user.role === UserRole.KASIR) && currentPath !== '/index') {
        navigate('/index');
      } else if (user.role === UserRole.KOKI && currentPath !== '/kitchen') {
        navigate('/kitchen');
      } else if (user.role === UserRole.PELAYAN && currentPath !== '/index') {
        navigate('/index');
      }
    }
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      let user = null;
      if (formData.username === 'admin' && formData.password === 'password123') {
        user = { username: 'admin', role: UserRole.ADMIN };
      } else if (formData.username === 'kasir' && formData.password === 'password123') {
        user = { username: 'kasir', role: UserRole.KASIR };
      } else if (formData.username === 'koki' && formData.password === 'password123') {
        user = { username: 'koki', role: UserRole.KOKI };
      } else if (formData.username === 'pelayan' && formData.password === 'password123') {
        user = { username: 'pelayan', role: UserRole.PELAYAN };
      }
      if (user) {
        localStorage.setItem('user', JSON.stringify(user));
        toast({
          title: "Login berhasil!",
          description: `Selamat datang, ${user.username}`,
        });
        // Redirect sesuai role
        if (user.role === UserRole.ADMIN || user.role === UserRole.KASIR) {
          navigate('/index');
        } else if (user.role === UserRole.KOKI) {
          navigate('/kitchen');
        } else if (user.role === UserRole.PELAYAN) {
          navigate('/index'); // Atur sesuai kebutuhan
        }
      } else {
        toast({
          title: "Login gagal!",
          description: "Username atau password salah",
          variant: "destructive",
        });
      }
      setIsLoading(false);
    }, 1000);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <Store className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold">Restoran POS</CardTitle>
          <CardDescription>Masuk ke sistem Point of Sale</CardDescription>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="username"
                  name="username"
                  type="text"
                  placeholder="Masukkan username"
                  value={formData.username}
                  onChange={handleInputChange}
                  className="pl-10"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Masukkan password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="pl-10 pr-10"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="rememberMe"
                checked={formData.rememberMe}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, rememberMe: checked as boolean }))
                }
              />
              <Label htmlFor="rememberMe" className="text-sm font-normal">
                Ingat saya
              </Label>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Masuk...' : 'Masuk'}
            </Button>

            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={() => navigate('/register')}
            >
              Daftar
            </Button>

            <div className="text-center">
              <Button variant="link" className="text-sm">
                Lupa password?
              </Button>
            </div>
          </form>

          <div className="mt-6 pt-6 border-t">
            <div className="text-center text-sm text-gray-600">
              <p>Demo credentials:</p>
              <p><strong>Username:</strong> admin</p>
              <p><strong>Password:</strong> password</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Login;
