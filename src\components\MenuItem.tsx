
import { useState } from 'react';
import { MenuItem } from '../types/pos';
import { formatCurrency } from '../utils/posData';
import { Plus, Minus } from 'lucide-react';

interface MenuItemProps {
  item: MenuItem;
  quantity: number;
  onQuantityChange: (itemId: string, quantity: number) => void;
}

const MenuItemComponent = ({ item, quantity, onQuantityChange }: MenuItemProps) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleIncrement = () => {
    onQuantityChange(item.id, quantity + 1);
  };

  const handleDecrement = () => {
    if (quantity > 0) {
      onQuantityChange(item.id, quantity - 1);
    }
  };

  return (
    <div
      className={`pos-card p-4 transition-all duration-200 ${
        isHovered ? 'transform scale-105' : ''
      } ${!item.available ? 'opacity-50' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex flex-col h-full">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-800 mb-2">{item.name}</h3>
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {item.description}
          </p>
          <div className="text-lg font-bold text-primary mb-3">
            {formatCurrency(item.price)}
          </div>
        </div>
        
        {item.available && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={handleDecrement}
                className="w-16 h-16 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors text-3xl font-bold shadow-lg"
                disabled={quantity === 0}
              >
                <Minus className="w-8 h-8" />
              </button>

              <span className="w-12 text-center font-bold text-2xl">{quantity}</span>

              <button
                onClick={handleIncrement}
                className="w-16 h-16 rounded-full bg-primary hover:bg-primary/90 text-white flex items-center justify-center transition-colors text-3xl font-bold shadow-lg"
              >
                <Plus className="w-8 h-8" />
              </button>
            </div>
          </div>
        )}
        
        {!item.available && (
          <div className="text-center py-2">
            <span className="text-red-500 font-medium">Tidak Tersedia</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default MenuItemComponent;
