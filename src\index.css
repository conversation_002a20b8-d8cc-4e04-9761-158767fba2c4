
@tailwind base;
@tailwind components;
@tailwind utilities;

/* POS Restaurant Design System */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 142 76% 36%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 24 95% 53%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 76% 36%;

    --radius: 0.5rem;

    --warning: 38 92% 50%;
    --warning-foreground: 222.2 47.4% 11.2%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 142 76% 36%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 24 95% 53%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 76% 36%;

    --warning: 38 92% 50%;
    --warning-foreground: 222.2 47.4% 11.2%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}

@layer components {
  .table-available {
    @apply bg-gradient-to-br from-emerald-50 to-green-100 border-emerald-200 hover:from-emerald-100 hover:to-green-200;
  }

  .table-occupied {
    @apply bg-gradient-to-br from-rose-50 to-red-100 border-rose-200 hover:from-rose-100 hover:to-red-200;
  }

  .table-reserved {
    @apply bg-gradient-to-br from-amber-50 to-yellow-100 border-amber-200 hover:from-amber-100 hover:to-yellow-200;
  }

  .pos-card {
    @apply bg-white rounded-xl shadow-lg border-2 border-gray-200 hover:shadow-2xl transition-all duration-300;
  }

  .pos-card-enhanced {
    @apply rounded-xl shadow-lg border-2 transition-all duration-300 hover:shadow-2xl hover:scale-105;
  }

  .pos-button {
    @apply bg-gradient-to-r from-primary to-primary/90 text-primary-foreground px-4 py-2 rounded-lg hover:from-primary/90 hover:to-primary shadow-md transition-all duration-200 hover:shadow-lg;
  }

  .pos-button-secondary {
    @apply bg-gradient-to-r from-secondary to-secondary/90 text-secondary-foreground px-4 py-2 rounded-lg hover:from-secondary/90 hover:to-secondary shadow-md transition-all duration-200 hover:shadow-lg;
  }

  .table-card-glow {
    @apply relative overflow-hidden;
  }

  .table-card-glow::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full transition-transform duration-1000;
  }

  .table-card-glow:hover::before {
    @apply translate-x-full;
  }
}

@layer utilities {
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  @keyframes slideIn {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* Print Styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  nav, 
  .no-print,
  button {
    display: none !important;
  }
  
  #receipt-content {
    box-shadow: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  .container {
    max-width: none !important;
    padding: 0 !important;
  }
  
  .page-break {
    page-break-after: always;
  }
}
