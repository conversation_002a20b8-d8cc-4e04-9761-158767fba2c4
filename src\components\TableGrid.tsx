
import { Table } from '../types/pos';
import TableCard from './TableCard';

interface TableGridProps {
  tables: Table[];
  onTableClick: (table: Table) => void;
  onViewOrderDetails?: (table: Table) => void;
  onEditTable?: (table: Table) => void;
  onDeleteTable?: (table: Table) => void;
}

const TableGrid = ({ tables, onTableClick, onViewOrderDetails, onEditTable, onDeleteTable }: TableGridProps) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {tables.map((table) => (
        <TableCard
          key={table.id}
          table={table}
          onTableClick={onTableClick}
          onViewOrderDetails={onViewOrderDetails}
          onEditTable={onEditTable}
          onDeleteTable={onDeleteTable}
        />
      ))}
    </div>
  );
};

export default TableGrid;
