
export interface Table {
  id: string;
  number: number;
  seats: number;
  status: 'available' | 'occupied' | 'reserved';
  orders?: Order[]; // Daftar pesanan aktif pada meja ini
  reservationTime?: string;
}

export interface MenuItem {
  id: string;
  name: string;
  price: number;
  category: string;
  image?: string;
  description?: string;
  available: boolean;
}

export interface OrderItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  notes?: string;
  discount?: number; // nominal (Rp) or percent
  discountType?: 'percent' | 'nominal';
}

export interface Order {
  id: string;
  noStruk: string; // Nomor Struk
  tableId: string;
  items?: OrderItem[]; // for legacy compatibility
  orderItems: OrderItem[];
  total: number;
  status: 'pending' | 'preparing' | 'ready' | 'served';
  createdAt: Date;
  updatedAt: Date;
  customer?: { name: string; phone?: string };
  createdBy?: string;
}

export interface MenuCategory {
  id: string;
  name: string;
  icon?: string;
  color?: string;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  KASIR = 'KASIR',
  KOKI = 'KOKI',
  PELAYAN = 'PELAYAN',
}

export interface User {
  id: string;
  name: string;
  username: string;
  email: string;
  password: string;
  role: UserRole;
  status: string;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}
