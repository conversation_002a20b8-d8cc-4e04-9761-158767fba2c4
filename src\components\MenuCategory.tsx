
import { MenuCategory } from '../types/pos';

interface MenuCategoryProps {
  category: MenuCategory;
  isActive: boolean;
  onClick: (categoryId: string) => void;
}

const MenuCategoryComponent = ({ category, isActive, onClick }: MenuCategoryProps) => {
  return (
    <button
      onClick={() => onClick(category.id)}
      className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
        isActive
          ? 'bg-primary text-primary-foreground shadow-md'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
    >
      <div className="flex items-center space-x-2">
        <span className="text-lg">{category.icon}</span>
        <span>{category.name}</span>
      </div>
    </button>
  );
};

export default MenuCategoryComponent;
