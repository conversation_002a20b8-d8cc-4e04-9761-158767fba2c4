import React from 'react';

interface OrderDetailModalProps {
  order: any;
  onClose: () => void;
}

const OrderDetailModal: React.FC<OrderDetailModalProps> = ({ order, onClose }) => {
  if (!order) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-lg p-6 relative animate-slide-in">
        <button onClick={onClose} className="absolute top-2 right-2 p-2 hover:bg-gray-100 rounded-full">✕</button>
        <h2 className="text-xl font-bold mb-2">Detail Pesanan</h2>
        <div className="mb-2"><span className="font-semibold">No Struk:</span> {order.noStruk}</div>
        <div className="mb-2"><span className="font-semibold">Customer:</span> {order.customer?.name || '-'}</div>
        <div className="mb-2"><span className="font-semibold">Status:</span> <span className="capitalize">{order.status}</span></div>
        <div className="mb-2"><span className="font-semibold">Total:</span> Rp{order.total.toLocaleString('id-ID')}</div>
        <div className="mb-4"><span className="font-semibold">Waktu:</span> {new Date(order.createdAt).toLocaleString('id-ID')}</div>
        {order.orderItems && order.orderItems.length > 0 && (
          <div>
            <div className="font-semibold mb-1">Daftar Item:</div>
            <ul className="list-disc pl-5">
              {order.orderItems.map((item: any) => (
                <li key={item.id}>
                  {item.menuItem?.name || '-'} x {item.quantity}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderDetailModal; 