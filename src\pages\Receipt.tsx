
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Printer, Download, ArrowLeft } from 'lucide-react';
import { Order } from '../types/pos';
import { formatCurrency } from '../utils/posData';
import Navigation from '../components/Navigation';

// POS 58mm print style
const receiptPrintStyle = `
@media print {
  body * { visibility: hidden !important; }
  #receipt-content, #receipt-content * { visibility: visible !important; }
  #receipt-content {
    position: absolute !important;
    left: 0; top: 0; width: 210px !important; min-width: 210px !important; max-width: 210px !important;
    font-size: 11px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
  }
}
`;

const SETTINGS_KEY = 'resto_settings';
const defaultSettings = {
  storeName: 'Restoran Lovable',
  storeAddress: 'Jl. Merdeka No. 123, Jakarta',
  storePhone: '(021) 1234-5678',
  storeLogo: '',
  taxRate: 0.11,
  taxLabel: 'PPN 11%',
  serviceChargeRate: 0.05,
  serviceChargeLabel: 'Service Charge 5%',
  discountLabel: 'Diskon Promo',
  footer1: 'Terima kasih atas kunjungan Anda!',
  footer2: 'Selamat menikmati hidangan',
};

const getSettings = () => {
  const saved = localStorage.getItem(SETTINGS_KEY);
  if (saved) return JSON.parse(saved);
  return defaultSettings;
};

const Receipt = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const [order, setOrder] = useState<Order | null>(null);
  const settings = getSettings();

  useEffect(() => {
    window.print();
  }, []);

  useEffect(() => {
    // Simulasi pengambilan data pesanan berdasarkan ID
    const mockOrder: Order = {
      id: orderId || 'order-1',
      noStruk: orderId || 'order-1', // Added noStruk for mock data
      tableId: '1',
      items: [
        {
          id: 'item-1',
          menuItem: {
            id: '1',
            name: 'Nasi Goreng Special',
            price: 35000,
            category: 'Makanan Utama',
            available: true
          },
          quantity: 2,
          notes: 'Pedas sedang'
        },
        {
          id: 'item-2',
          menuItem: {
            id: '2',
            name: 'Es Teh Manis',
            price: 8000,
            category: 'Minuman',
            available: true
          },
          quantity: 2
        }
      ],
      orderItems: [
        {
          id: 'item-1',
          menuItem: {
            id: '1',
            name: 'Nasi Goreng Special',
            price: 35000,
            category: 'Makanan Utama',
            available: true
          },
          quantity: 2,
          notes: 'Pedas sedang'
        },
        {
          id: 'item-2',
          menuItem: {
            id: '2',
            name: 'Es Teh Manis',
            price: 8000,
            category: 'Minuman',
            available: true
          },
          quantity: 2
        }
      ],
      total: 86000,
      status: 'served',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    setOrder(mockOrder);
  }, [orderId]);

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Simulasi download PDF
    const element = document.getElementById('receipt-content');
    if (element) {
      // Implementasi download PDF bisa menggunakan library seperti html2pdf
      console.log('Download PDF functionality would be implemented here');
    }
  };

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-6 py-6">
          <div className="text-center">
            <p>Pesanan tidak ditemukan</p>
          </div>
        </div>
      </div>
    );
  }

  // Tax rate (e.g. 10%)
  const items = order.orderItems || order.items || [];
  const subtotal = items.reduce((sum, item) => {
    let price = item.menuItem.price;
    if (item.discount && item.discount > 0) {
      if (item.discountType === 'percent') {
        price = price - (price * item.discount / 100);
      } else {
        price = price - item.discount;
      }
    }
    return sum + (price * item.quantity);
  }, 0);
  const serviceCharge = Math.round(subtotal * settings.serviceChargeRate);
  const tax = Math.round((subtotal + serviceCharge) * settings.taxRate);
  const grandTotal = subtotal + serviceCharge + tax;

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-6 py-6">
        <div className="max-w-2xl mx-auto">
          {/* Header Actions */}
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Kembali</span>
            </button>
            
            <div className="flex space-x-3">
              <button
                onClick={handlePrint}
                className="flex items-center space-x-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Printer className="w-4 h-4" />
                <span>Cetak</span>
              </button>
              <button
                onClick={handleDownload}
                className="flex items-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </button>
            </div>
          </div>

          {/* Receipt Content */}
          <>
            <style>{receiptPrintStyle}</style>
            <div id="receipt-content" className="bg-white rounded-lg shadow-sm border p-8">
              {/* Restaurant Header */}
              <div className="text-center mb-8">
                {settings.storeLogo && (
                  <img src={settings.storeLogo} alt="Logo" className="mx-auto mb-2 max-h-12" />
                )}
                <h1 className="text-2xl font-bold text-gray-800 mb-2">{settings.storeName}</h1>
                <p className="text-gray-600">{settings.storeAddress}</p>
                <p className="text-gray-600">Telp: {settings.storePhone}</p>
              </div>

              {/* Order Info */}
              <div className="border-b pb-4 mb-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">No. Pesanan</p>
                    <p className="font-medium">{order.noStruk}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Meja</p>
                    <p className="font-medium">{order.tableId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Tanggal</p>
                    <p className="font-medium">
                      {new Date().toLocaleDateString('id-ID', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Waktu</p>
                    <p className="font-medium">
                      {new Date().toLocaleTimeString('id-ID', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3">Detail Pesanan</h3>
                <div className="space-y-2">
                  {items.map(item => (
                    <div key={item.id} className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <span className="font-medium">{item.quantity}x</span>
                          <span className="ml-2">{item.menuItem.name}</span>
                        </div>
                        {item.notes && (
                          <p className="text-sm text-gray-600 ml-6 italic">
                            Note: {item.notes}
                          </p>
                        )}
                        {item.discount && item.discount > 0 && (
                          <p className="text-xs text-green-600 ml-6">
                            {settings.discountLabel}: {item.discountType === 'percent' ? `${item.discount}%` : `Rp${item.discount}`}
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency((() => {
                          let price = item.menuItem.price;
                          if (item.discount && item.discount > 0) {
                            if (item.discountType === 'percent') {
                              price = price - (price * item.discount / 100);
                            } else {
                              price = price - item.discount;
                            }
                          }
                          return price * item.quantity;
                        })())}</p>
                        <p className="text-sm text-gray-600">
                          @{formatCurrency(item.menuItem.price)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Total */}
              <div className="border-t pt-4 text-sm">
                <div className="flex justify-between items-center mb-1">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between items-center mb-1">
                  <span>{settings.serviceChargeLabel}:</span>
                  <span>{formatCurrency(serviceCharge)}</span>
                </div>
                <div className="flex justify-between items-center mb-1">
                  <span>{settings.taxLabel}:</span>
                  <span>{formatCurrency(tax)}</span>
                </div>
                <div className="flex justify-between items-center text-lg font-bold mt-2">
                  <span>Total Pembayaran:</span>
                  <span>{formatCurrency(grandTotal)}</span>
                </div>
              </div>

              {/* Footer */}
              <div className="text-center mt-8 pt-6 border-t">
                <p className="text-sm text-gray-600 mb-2">
                  {settings.footer1}
                </p>
                <p className="text-sm text-gray-600">
                  {settings.footer2}
                </p>
              </div>
            </div>
          </>
        </div>
      </div>
    </div>
  );
};

export default Receipt;
