import { useEffect, useState } from 'react';
import TableGrid from '../components/TableGrid';
import { Table } from '../types/pos';

interface Order {
  id: string;
  tableId: string;
  customer?: { name: string; phone?: string };
  status: string;
  total: number;
  createdAt: string;
  table: { id: string; number: number };
  noStruk: string;
}

const OnlineOrders = () => {
  const [tab, setTab] = useState<'layout' | 'online'>('layout');
  const [tables, setTables] = useState<Table[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTables = async () => {
      try {
        const res = await fetch('/api/tables');
        const data = await res.json();
        setTables(data.filter((table: Table) => !table.id.startsWith('online-') && !table.id.startsWith('takeaway-')));
      } catch {}
    };
    const fetchOrders = async () => {
      setLoading(true);
      try {
        const res = await fetch('/api/orders');
        const data = await res.json();
        const filtered = data.filter((order: Order) => order.tableId.startsWith('online-') || order.tableId.startsWith('takeaway-'));
        setOrders(filtered);
      } catch (err) {
        setOrders([]);
      } finally {
        setLoading(false);
      }
    };
    fetchTables();
    fetchOrders();
  }, []);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Pesanan Online & Take Away</h1>
      <div className="mb-4">
        <div className="flex gap-0 border-b border-gray-300 rounded-t overflow-hidden w-max">
          <button
            className={`px-6 py-2 transition-all duration-150 focus:outline-none ${tab === 'layout' ? 'bg-white border-b-2 border-blue-500 font-bold text-blue-700 shadow-sm' : 'bg-gray-100 text-gray-500 hover:bg-gray-200'}`}
            onClick={() => setTab('layout')}
          >
            Layout Meja
          </button>
          <button
            className={`px-6 py-2 transition-all duration-150 focus:outline-none ${tab === 'online' ? 'bg-white border-b-2 border-blue-500 font-bold text-blue-700 shadow-sm' : 'bg-gray-100 text-gray-500 hover:bg-gray-200'}`}
            onClick={() => setTab('online')}
          >
            Pesanan Online/Take Away
          </button>
        </div>
      </div>
      <div className="bg-white p-4 rounded shadow">
        {tab === 'layout' ? (
          <TableGrid tables={tables} onTableClick={() => {}} />
        ) : loading ? (
          <div>Loading...</div>
        ) : orders.length === 0 ? (
          <div>Tidak ada pesanan online/takeaway.</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full border">
              <thead>
                <tr className="bg-gray-100">
                  <th className="px-4 py-2 border">Jenis</th>
                  <th className="px-4 py-2 border">ID Pesanan</th>
                  <th className="px-4 py-2 border">Customer</th>
                  <th className="px-4 py-2 border">Status</th>
                  <th className="px-4 py-2 border">Total</th>
                  <th className="px-4 py-2 border">Waktu</th>
                </tr>
              </thead>
              <tbody>
                {orders.map(order => (
                  <tr key={order.id}>
                    <td className="px-4 py-2 border text-center font-semibold">
                      {order.tableId.startsWith('online-') ? 'Online' : 'Take Away'}
                    </td>
                    <td className="px-4 py-2 border">{order.noStruk}</td>
                    <td className="px-4 py-2 border">{order.customer?.name || '-'}</td>
                    <td className="px-4 py-2 border capitalize">{order.status}</td>
                    <td className="px-4 py-2 border">Rp{order.total.toLocaleString('id-ID')}</td>
                    <td className="px-4 py-2 border">{new Date(order.createdAt).toLocaleString('id-ID')}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default OnlineOrders; 