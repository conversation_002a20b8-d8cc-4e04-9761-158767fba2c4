
import { useState, useEffect } from 'react';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import { Label } from '../components/ui/label';
import { useToast } from '../hooks/use-toast';

const SETTINGS_KEY = 'resto_settings';

const defaultSettings = {
  storeName: 'Restoran Lovable',
  storeAddress: 'Jl. Merdeka No. 123, Jakarta',
  storePhone: '(021) 1234-5678',
  storeLogo: '',
  taxRate: 0.11,
  taxLabel: 'PPN 11%',
  serviceChargeRate: 0.05,
  serviceChargeLabel: 'Service Charge 5%',
  discountLabel: 'Diskon Promo',
  footer1: 'Terima kasih atas kunjungan Anda!',
  footer2: 'Selamat menikmati hidangan',
};

export default function Settings() {
  const [settings, setSettings] = useState(defaultSettings);
  const { toast } = useToast();

  useEffect(() => {
    const saved = localStorage.getItem(SETTINGS_KEY);
    if (saved) setSettings(JSON.parse(saved));
  }, []);

  const handleChange = (e: any) => {
    const { name, value, type } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) : value
    }));
  };

  const handleLogoChange = (e: any) => {
    const file = e.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (ev: any) => {
      setSettings(prev => ({ ...prev, storeLogo: ev.target.result }));
    };
    reader.readAsDataURL(file);
  };

  const handleSave = () => {
    localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
    toast({ title: 'Pengaturan disimpan', description: 'Pengaturan berhasil diperbarui.' });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-xl mx-auto bg-white rounded-lg shadow p-8">
        <h1 className="text-2xl font-bold mb-6">Pengaturan Toko & Struk</h1>
        <div className="space-y-4">
          <div>
            <Label>Nama Toko</Label>
            <Input name="storeName" value={settings.storeName} onChange={handleChange} />
          </div>
          <div>
            <Label>Alamat</Label>
            <Input name="storeAddress" value={settings.storeAddress} onChange={handleChange} />
          </div>
          <div>
            <Label>Telepon</Label>
            <Input name="storePhone" value={settings.storePhone} onChange={handleChange} />
          </div>
          <div>
            <Label>Logo (opsional)</Label>
            <Input type="file" accept="image/*" onChange={handleLogoChange} />
            {settings.storeLogo && <img src={settings.storeLogo} alt="Logo" className="mt-2 max-h-16" />}
          </div>
          <div>
            <Label>Persentase Pajak (%)</Label>
            <Input name="taxRate" type="number" min="0" max="1" step="0.01" value={settings.taxRate} onChange={handleChange} />
          </div>
          <div>
            <Label>Label Pajak</Label>
            <Input name="taxLabel" value={settings.taxLabel} onChange={handleChange} />
          </div>
          <div>
            <Label>Persentase Service Charge (%)</Label>
            <Input name="serviceChargeRate" type="number" min="0" max="1" step="0.01" value={settings.serviceChargeRate} onChange={handleChange} />
          </div>
          <div>
            <Label>Label Service Charge</Label>
            <Input name="serviceChargeLabel" value={settings.serviceChargeLabel} onChange={handleChange} />
          </div>
          <div>
            <Label>Label Diskon</Label>
            <Input name="discountLabel" value={settings.discountLabel} onChange={handleChange} />
          </div>
          <div>
            <Label>Footer 1</Label>
            <Input name="footer1" value={settings.footer1} onChange={handleChange} />
          </div>
          <div>
            <Label>Footer 2</Label>
            <Input name="footer2" value={settings.footer2} onChange={handleChange} />
          </div>
        </div>
        <Button className="mt-8 w-full" onClick={handleSave}>Simpan Pengaturan</Button>
      </div>
    </div>
  );
}
