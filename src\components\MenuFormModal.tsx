import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogDescription } from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Button } from './ui/button';
import { Switch } from './ui/switch';

interface StockItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  cost: number;
  supplier?: string;
}

interface MenuFormModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  initialData?: any;
  categories: string[];
  stockItems?: StockItem[];
}

const MenuFormModal = ({ open, onClose, onSubmit, initialData, categories, stockItems }: MenuFormModalProps) => {
  const [form, setForm] = useState<any>({
    name: '',
    description: '',
    price: '',
    category: '',
    image: '',
    available: true,
    preparationTime: '',
    ingredients: '',
  });
  const [ingredients, setIngredients] = useState<{ name: string; qty: number; unit: string }[]>([]);

  useEffect(() => {
    if (initialData) {
      // Set form data
      const categoryValue = typeof initialData.category === 'object' && initialData.category !== null
        ? initialData.category.name
        : initialData.category || '';

      setForm({
        name: initialData.name || '',
        description: initialData.description || '',
        price: initialData.price || '',
        category: categoryValue,
        image: initialData.image || '',
        available: initialData.available !== undefined ? initialData.available : true,
        preparationTime: initialData.preparationTime || '',
        ingredients: '',
      });

      // Set ingredients data
      let ingredientsData = [];
      if (initialData.ingredients) {
        try {
          // Handle both string (JSON) and array formats
          if (typeof initialData.ingredients === 'string') {
            ingredientsData = JSON.parse(initialData.ingredients);
          } else if (Array.isArray(initialData.ingredients)) {
            ingredientsData = initialData.ingredients;
          }
        } catch (error) {
          console.error('Error parsing ingredients:', error);
          ingredientsData = [];
        }
      }

      if (Array.isArray(ingredientsData)) {
        setIngredients(ingredientsData.map((b: any) => typeof b === 'string' ? { name: b, qty: 1, unit: '' } : b));
      } else {
        setIngredients([]);
      }
    } else {
      // Reset form when no initial data
      setForm({
        name: '',
        description: '',
        price: '',
        category: '',
        image: '',
        available: true,
        preparationTime: '',
        ingredients: '',
      });
      setIngredients([]);
    }
  }, [initialData, open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type, checked } = e.target as any;
    setForm((prev: any) => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };

  const handleSwitch = (checked: boolean) => {
    setForm((prev: any) => ({ ...prev, available: checked }));
  };

  const handleAddIngredient = () => {
    setIngredients([...ingredients, { name: '', qty: 1, unit: '' }]);
  };
  const handleIngredientChange = (idx: number, field: 'name' | 'qty' | 'unit', value: string) => {
    setIngredients(ings => ings.map((ing, i) => {
      if (i === idx) {
        if (field === 'name') {
          // Auto-fill unit when selecting ingredient
          const stockItem = stockItems?.find(item => item.name === value);
          return {
            ...ing,
            [field]: value,
            unit: stockItem ? stockItem.unit : ing.unit
          };
        } else {
          return { ...ing, [field]: field === 'qty' ? Number(value) : value };
        }
      }
      return ing;
    }));
  };
  const handleRemoveIngredient = (idx: number) => {
    setIngredients(ings => ings.filter((_, i) => i !== idx));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validasi stok untuk setiap bahan
    const stockWarnings: string[] = [];
    const stockErrors: string[] = [];

    ingredients.forEach((ing, idx) => {
      const stockItem = stockItems?.find(item => item.name === ing.name);
      if (stockItem) {
        if (stockItem.currentStock === 0) {
          stockErrors.push(`${ing.name} sudah habis!`);
        } else if (stockItem.currentStock <= stockItem.minStock) {
          stockWarnings.push(`${ing.name} stok rendah (${stockItem.currentStock} ${stockItem.unit})`);
        }
      }
    });

    if (stockErrors.length > 0) {
      alert(`Tidak dapat menyimpan menu:\n${stockErrors.join('\n')}`);
      return;
    }

    if (stockWarnings.length > 0) {
      const proceed = confirm(`Peringatan stok rendah:\n${stockWarnings.join('\n')}\n\nLanjutkan menyimpan menu?`);
      if (!proceed) return;
    }

    onSubmit({
      ...form,
      price: Number(form.price),
      preparationTime: Number(form.preparationTime),
      ingredients,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{initialData ? 'Edit Menu' : 'Tambah Menu Baru'}</DialogTitle>
          <DialogDescription>
            {initialData ? 'Ubah informasi menu yang sudah ada' : 'Tambahkan menu baru ke dalam sistem'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Nama Menu</Label>
              <Input id="name" name="name" value={form.name ?? ""} onChange={handleChange} required />
            </div>
            <div>
              <Label htmlFor="category">Kategori</Label>
              <select
                id="category"
                name="category"
                value={form.category ?? ""}
                onChange={handleChange}
                className="w-full p-2 border rounded-md"
                required
              >
                <option value="">Pilih kategori</option>
                {categories.filter(c => c !== 'all').map(c => (
                  <option key={c} value={c}>{c}</option>
                ))}
              </select>
            </div>
          </div>
          <div>
            <Label htmlFor="description">Deskripsi</Label>
            <textarea
              id="description"
              name="description"
              className="w-full p-2 border rounded-md"
              rows={3}
              placeholder="Deskripsi menu..."
              value={form.description ?? ""}
              onChange={handleChange}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="price">Harga</Label>
              <Input id="price" name="price" type="number" value={form.price ?? ""} onChange={handleChange} required />
            </div>
            <div>
              <Label htmlFor="preparationTime">Waktu Persiapan (menit)</Label>
              <Input id="preparationTime" name="preparationTime" type="number" value={form.preparationTime ?? ""} onChange={handleChange} />
            </div>
          </div>
          <div>
            <Label htmlFor="ingredients">Bahan-bahan</Label>
            <div className="space-y-2">
              {ingredients.map((ing, idx) => {
                const selectedStock = stockItems?.find(item => item.name === ing.name);
                const isLowStock = selectedStock && selectedStock.currentStock <= selectedStock.minStock;
                const isOutOfStock = selectedStock && selectedStock.currentStock === 0;

                return (
                  <div key={idx} className="flex gap-2 items-center">
                    <div className="flex-1">
                      <select
                        className={`p-2 border rounded-md w-full ${isOutOfStock ? 'border-red-500 bg-red-50' : isLowStock ? 'border-yellow-500 bg-yellow-50' : ''}`}
                        value={ing.name}
                        onChange={e => handleIngredientChange(idx, 'name', e.target.value)}
                        required
                      >
                        <option value="">Pilih bahan</option>
                        {stockItems && stockItems.length > 0 ? (
                          stockItems.map(item => (
                            <option key={item.name} value={item.name}>
                              {item.name} - Stok: {item.currentStock} {item.unit}
                              {item.currentStock <= item.minStock ? ' (Stok Rendah!)' : ''}
                            </option>
                          ))
                        ) : (
                          <option disabled>Loading stock items...</option>
                        )}
                      </select>
                      {selectedStock && (
                        <div className="text-xs mt-1">
                          <span className={`${isOutOfStock ? 'text-red-600' : isLowStock ? 'text-yellow-600' : 'text-gray-600'}`}>
                            Tersedia: {selectedStock.currentStock} {selectedStock.unit}
                            {isOutOfStock && ' - HABIS!'}
                            {isLowStock && !isOutOfStock && ' - Stok Rendah!'}
                          </span>
                        </div>
                      )}
                    </div>
                    <Input
                      type="number"
                      min={0.1}
                      step="0.1"
                      className="w-20"
                      value={ing.qty}
                      onChange={e => handleIngredientChange(idx, 'qty', e.target.value)}
                      placeholder="Qty"
                      required
                    />
                    <Input
                      className="w-24"
                      value={selectedStock ? selectedStock.unit : ing.unit}
                      onChange={e => handleIngredientChange(idx, 'unit', e.target.value)}
                      placeholder="Satuan"
                      readOnly={!!selectedStock}
                      required
                    />
                    <Button type="button" variant="destructive" size="sm" onClick={() => handleRemoveIngredient(idx)}>-</Button>
                  </div>
                );
              })}
              <Button type="button" variant="outline" size="sm" onClick={handleAddIngredient}>+ Tambah Bahan</Button>

              {/* Ringkasan Stok */}
              {ingredients.length > 0 && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                  <h4 className="font-medium text-sm mb-2">Ringkasan Kebutuhan Stok:</h4>
                  <div className="space-y-1">
                    {ingredients.map((ing, idx) => {
                      const stockItem = stockItems?.find(item => item.name === ing.name);
                      if (!stockItem) return null;

                      const canMake = Math.floor(stockItem.currentStock / ing.qty);
                      const isLowStock = stockItem.currentStock <= stockItem.minStock;
                      const isOutOfStock = stockItem.currentStock === 0;

                      return (
                        <div key={idx} className="flex justify-between text-xs">
                          <span>{ing.name}: {ing.qty} {stockItem.unit}</span>
                          <span className={`${isOutOfStock ? 'text-red-600' : isLowStock ? 'text-yellow-600' : 'text-green-600'}`}>
                            Dapat buat: {isOutOfStock ? '0' : canMake} porsi
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
          <div>
            <Label htmlFor="image">Gambar Menu (URL)</Label>
            <Input id="image" name="image" value={form.image ?? ""} onChange={handleChange} placeholder="/placeholder.svg" />
          </div>
          <div className="flex items-center space-x-2">
            <Switch id="available" checked={form.available} onCheckedChange={handleSwitch} />
            <Label htmlFor="available">Tersedia</Label>
          </div>
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>Batal</Button>
            <Button type="submit">{initialData ? 'Simpan' : 'Tambah'}</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default MenuFormModal; 