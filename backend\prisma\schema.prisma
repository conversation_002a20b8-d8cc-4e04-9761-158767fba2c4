// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model MenuCategory {
  id        String   @id @default(cuid())
  name      String   @unique
  icon      String?
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  menuItems MenuItem[]

  @@map("menu_categories")
}

model MenuItem {
  id              String   @id @default(cuid())
  name            String
  description     String?
  price           Float
  image           String?
  available       Boolean  @default(true)
  preparationTime Int?     // in minutes
  ingredients     String?  // JSON string of ingredients array
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  categoryId String
  category   MenuCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]

  @@map("menu_items")
}

model Table {
  id             String   @id @default(cuid())
  number         Int      @unique
  seats          Int
  status         String   @default("available") // available, occupied, reserved
  reservationTime String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  orders Order[]

  @@map("tables")
}

model Order {
  id        String   @id @default(cuid())
  noStruk   String   @unique // Nomor Struk
  tableId   String
  customerId String?
  customer   Customer? @relation(fields: [customerId], references: [id])
  total     Float
  status    String   @default("pending") // pending, preparing, ready, served, completed, cancelled
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?

  // Relations
  table      Table       @relation(fields: [tableId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]

  @@map("orders")
}

model OrderItem {
  id       String @id @default(cuid())
  quantity Int
  notes    String?
  price    Float  // Price at time of order

  // Relations
  orderId   String
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  menuItemId String
  menuItem  MenuItem @relation(fields: [menuItemId], references: [id])

  @@map("order_items")
}

model User {
  id        String   @id @default(cuid())
  name      String
  username  String   @unique
  email     String   @unique
  password  String
  role      UserRole
  status    String   @default("active") // active, inactive
  lastLogin DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum UserRole {
  ADMIN
  KASIR
  KOKI
  PELAYAN
}

model StockItem {
  id           String   @id @default(cuid())
  name         String
  category     String
  currentStock Float
  minStock     Float
  maxStock     Float
  unit         String
  cost         Float
  supplier     String?
  lastUpdated  DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  stockMutations StockMutation[] // relasi balik ke StockMutation

  @@map("stock_items")
}

model Customer {
  id        String   @id @default(cuid())
  name      String
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  orders    Order[]

  @@map("customers")
}

model StockMutation {
  id              String    @id @default(cuid())
  stockItem       StockItem @relation(fields: [stockItemId], references: [id])
  stockItemId     String
  type            String    // "IN" atau "OUT"
  amount          Float
  user            String
  transactionName String?   // Nama transaksi (opsional)
  createdAt       DateTime  @default(now())
}
