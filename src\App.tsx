
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Kitchen from "./pages/Kitchen";
import Cashier from "./pages/Cashier";
import Receipt from "./pages/Receipt";
import Reports from "./pages/Reports";
import Login from "./pages/Login";
import Users from "./pages/Users";
import Stock from "./pages/Stock";
import StockHistory from "./pages/StockHistory";
import Menu from "./pages/Menu";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import OnlineOrders from './pages/OnlineOrders';
import Register from './pages/Register';
import { ProtectedRoute } from './components/ProtectedRoute';
import { UserRole } from './types/pos';

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Login />} />
          <Route path="/index" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.KASIR, UserRole.PELAYAN]}>
              <Index />
            </ProtectedRoute>
          } />
          <Route path="/kitchen" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.KOKI, UserRole.KASIR]}>
              <Kitchen />
            </ProtectedRoute>
          } />
          <Route path="/cashier" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.KASIR]}>
              <Cashier />
            </ProtectedRoute>
          } />
          <Route path="/receipt/:orderId" element={<Receipt />} />
          <Route path="/reports" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.KASIR]}>
              <Reports />
            </ProtectedRoute>
          } />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/users" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.KASIR]}>
              <Users />
            </ProtectedRoute>
          } />
          <Route path="/stock" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
              <Stock />
            </ProtectedRoute>
          } />
          <Route path="/stock-history" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
              <StockHistory />
            </ProtectedRoute>
          } />
          <Route path="/menu" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.KASIR]}>
              <Menu />
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.KASIR]}>
              <Settings />
            </ProtectedRoute>
          } />
          <Route path="/online-orders" element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.KASIR]}>
              <OnlineOrders />
            </ProtectedRoute>
          } />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
