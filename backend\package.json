{"name": "backend", "version": "1.0.0", "description": "Backend API for Restaurant POS System", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "ts-node src/seed.ts", "db:reset": "prisma migrate reset --force && npm run db:seed", "db:studio": "prisma studio"}, "keywords": ["restaurant", "pos", "api"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.11.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "prisma": "^6.11.1", "react-beautiful-dnd": "^13.1.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.12", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}