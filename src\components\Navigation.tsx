
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Home,
  ChefHat,
  CreditCard,
  BarChart3,
  LogIn,
  Users,
  Package,
  MenuIcon,
  Settings,
  Receipt,
  History
} from 'lucide-react';
import { UserRole } from '../types/pos';

const Navigation = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const navItems = [
    { path: '/index', label: 'Dashboard', icon: Home },
    { path: '/kitchen', label: 'Dapur', icon: ChefHat },
    { path: '/cashier', label: 'Kasir', icon: CreditCard },
    { path: '/menu', label: 'Menu', icon: MenuIcon },
    { path: '/stock', label: 'Stok', icon: Package },
    { path: '/stock-history', label: 'History Stok', icon: History },
    { path: '/reports', label: 'Laporan', icon: BarChart3 },
    { path: '/users', label: 'Pengguna', icon: Users },
    { path: '/settings', label: 'Pengaturan', icon: Settings },
  ];

  const isActive = (path: string) => location.pathname === path;

  const userStr = localStorage.getItem('user');
  const user = userStr ? JSON.parse(userStr) : null;
  let filteredNavItems = navItems;
  if (user) {
    if (user.role === UserRole.ADMIN) {
      filteredNavItems = navItems; // Admin sees all
    } else if (user.role === UserRole.KASIR) {
      filteredNavItems = navItems.filter(item => item.path !== '/reports' && item.path !== '/stock' && item.path !== '/stock-history' && item.path !== '/users' && item.path !== '/settings');
    } else if (user.role === UserRole.KOKI) {
      filteredNavItems = navItems.filter(item => item.path === '/kitchen');
    } else if (user.role === UserRole.PELAYAN) {
      filteredNavItems = navItems.filter(item => item.path === '/index');
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/login');
  };

  return (
    <nav className="bg-white border-b border-gray-200 shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-8">
            <Link to="/" className="flex items-center space-x-2">
              <Receipt className="h-8 w-8 text-primary" />
              <span className="text-xl font-bold">RestoPOS</span>
            </Link>
            
            <div className="hidden md:flex space-x-1">
              {filteredNavItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive(item.path)
                        ? 'bg-primary text-primary-foreground'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Link>
                );
              })}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              {new Date().toLocaleDateString('id-ID', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </span>
            {user && (
              <>
                <span className="text-sm text-gray-700 font-semibold mr-2">{user.username}</span>
                <button
                  onClick={handleLogout}
                  className="ml-2 px-3 py-1 rounded bg-red-500 text-white hover:bg-red-600 text-sm font-medium"
                >
                  Logout
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export { Navigation };
export default Navigation;
