
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Search, AlertTriangle, Package, TrendingDown, TrendingUp, History, Edit, Trash2, Eye } from 'lucide-react';
import { Navigation } from '@/components/Navigation';
import React from 'react';

interface StockItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  lastUpdated: string;
  supplier: string;
  cost: number;
}

// Component for individual stock row to avoid React.Fragment issues
const StockRow = ({ item, expandedItemId, setExpandedItemId, mutations, mutationsLoading, mutationsError, fetchMutations, openEditModal, openStockInModal, openStockOutModal, getStockStatus, openViewMutationModal, openEditMutationModal, handleDeleteMutation }: {
  item: StockItem;
  expandedItemId: string | null;
  setExpandedItemId: (id: string | null) => void;
  mutations: Record<string, any[]>;
  mutationsLoading: Record<string, boolean>;
  mutationsError: Record<string, string | null>;
  fetchMutations: (id: string) => void;
  openEditModal: (item: StockItem) => void;
  openStockInModal: (item: StockItem) => void;
  openStockOutModal: (item: StockItem) => void;
  getStockStatus: (current: number, min: number, max: number) => { label: string; variant: any };
  openViewMutationModal: (mutation: any) => void;
  openEditMutationModal: (mutation: any) => void;
  handleDeleteMutation: (mutationId: string) => void;
}) => {
  const status = getStockStatus(item.currentStock, item.minStock, item.maxStock);

  return (
    <>
      <TableRow
        onClick={() => {
          if (expandedItemId === item.id) {
            setExpandedItemId(null);
          } else {
            setExpandedItemId(item.id);
            if (!mutations[item.id]) fetchMutations(item.id);
          }
        }}
        className="cursor-pointer"
      >
        <TableCell className="font-medium">{item.name}</TableCell>
        <TableCell>{item.category}</TableCell>
        <TableCell>
          {item.currentStock} {item.unit}
        </TableCell>
        <TableCell className="text-sm text-muted-foreground">
          {item.minStock} / {item.maxStock}
        </TableCell>
        <TableCell>
          <Badge variant={status.variant}>{status.label}</Badge>
        </TableCell>
        <TableCell>{item.supplier}</TableCell>
        <TableCell className="text-sm text-muted-foreground">
          {new Date(item.lastUpdated).toLocaleDateString('id-ID')}
        </TableCell>
        <TableCell>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={e => { e.stopPropagation(); openEditModal(item); }}>Edit</Button>
            <Button size="sm" variant="outline" onClick={e => { e.stopPropagation(); openStockInModal(item); }}>Stock In</Button>
            <Button size="sm" variant="destructive" onClick={e => { e.stopPropagation(); openStockOutModal(item); }}>Stock Out</Button>
          </div>
        </TableCell>
      </TableRow>
      {expandedItemId === item.id && (
        <tr>
          <td colSpan={8} className="bg-gray-50 p-4">
            <div className="mb-3">
              <h4 className="font-semibold text-gray-800 flex items-center gap-2">
                <History className="h-4 w-4" />
                History Mutasi
              </h4>
            </div>
            {mutationsLoading[item.id] ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-sm text-gray-600">Loading...</span>
              </div>
            ) : mutationsError[item.id] ? (
              <div className="text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200">
                {mutationsError[item.id]}
              </div>
            ) : (
              <div className="space-y-2">
                {(mutations[item.id] || []).length === 0 ? (
                  <div className="text-center py-6 text-gray-500 italic">
                    Belum ada mutasi untuk item ini
                  </div>
                ) : (
                  <div className="max-h-64 overflow-y-auto">
                    <table className="w-full text-sm">
                      <thead className="bg-gray-100 sticky top-0">
                        <tr>
                          <th className="text-left p-2 font-medium text-gray-700">Tanggal & Waktu</th>
                          <th className="text-left p-2 font-medium text-gray-700">Tipe</th>
                          <th className="text-left p-2 font-medium text-gray-700">Jumlah</th>
                          <th className="text-left p-2 font-medium text-gray-700">Nama Transaksi</th>
                          <th className="text-left p-2 font-medium text-gray-700">User</th>
                          <th className="text-left p-2 font-medium text-gray-700">Aksi</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(mutations[item.id] || []).map((m, index) => (
                          <tr key={m.id} className={`border-b border-gray-200 hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                            <td className="p-2 text-gray-600">
                              {new Date(m.createdAt).toLocaleString('id-ID', {
                                day: '2-digit',
                                month: '2-digit',
                                year: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </td>
                            <td className="p-2">
                              <Badge
                                variant={m.type === 'IN' ? 'default' : 'destructive'}
                                className="text-xs"
                              >
                                {m.type === 'IN' ? 'Masuk' : 'Keluar'}
                              </Badge>
                            </td>
                            <td className="p-2">
                              <span className={`font-medium ${m.type === 'IN' ? 'text-green-600' : 'text-red-600'}`}>
                                {m.type === 'IN' ? '+' : '-'}{m.amount} {item.unit}
                              </span>
                            </td>
                            <td className="p-2">
                              {m.transactionName ? (
                                <span className="text-blue-600 bg-blue-50 px-2 py-1 rounded text-xs">
                                  {m.transactionName}
                                </span>
                              ) : (
                                <span className="text-gray-400 italic">-</span>
                              )}
                            </td>
                            <td className="p-2 text-gray-700 font-medium">
                              {m.user}
                            </td>
                            <td className="p-2">
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openViewMutationModal(m);
                                  }}
                                  className="h-7 w-7 p-0"
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openEditMutationModal(m);
                                  }}
                                  className="h-7 w-7 p-0"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteMutation(m.id);
                                  }}
                                  className="h-7 w-7 p-0"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </td>
        </tr>
      )}
    </>
  );
};

const Stock = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [stockItems, setStockItems] = useState<StockItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [form, setForm] = useState<Partial<StockItem>>({});
  const [editing, setEditing] = useState<StockItem | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [showStockInModal, setShowStockInModal] = useState(false);
  const [stockInItem, setStockInItem] = useState<StockItem | null>(null);
  const [stockInAmount, setStockInAmount] = useState<number>(0);
  const [stockInTransactionName, setStockInTransactionName] = useState<string>('');
  const [stockInLoading, setStockInLoading] = useState(false);
  const [stockInError, setStockInError] = useState<string | null>(null);
  const [expandedItemId, setExpandedItemId] = useState<string | null>(null);
  const [mutations, setMutations] = useState<Record<string, any[]>>({});
  const [mutationsLoading, setMutationsLoading] = useState<Record<string, boolean>>({});
  const [mutationsError, setMutationsError] = useState<Record<string, string | null>>({});
  const [showStockOutModal, setShowStockOutModal] = useState(false);
  const [stockOutItem, setStockOutItem] = useState<StockItem | null>(null);
  const [stockOutAmount, setStockOutAmount] = useState<number>(0);
  const [stockOutTransactionName, setStockOutTransactionName] = useState<string>('');
  const [stockOutLoading, setStockOutLoading] = useState(false);
  const [stockOutError, setStockOutError] = useState<string | null>(null);

  // Mutation CRUD states
  const [showEditMutationModal, setShowEditMutationModal] = useState(false);
  const [showViewMutationModal, setShowViewMutationModal] = useState(false);
  const [editingMutation, setEditingMutation] = useState<any>(null);
  const [mutationForm, setMutationForm] = useState<any>({});
  const [mutationFormLoading, setMutationFormLoading] = useState(false);
  const [mutationFormError, setMutationFormError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    fetch('/api/stock')
      .then(res => {
        if (!res.ok) throw new Error('Gagal mengambil data stok');
        return res.json();
      })
      .then(data => {
        setStockItems(data || []);
        setLoading(false);
      })
      .catch(err => {
        setError(err.message || 'Gagal mengambil data stok');
        setLoading(false);
      });
  }, []);

  const categories = ['all', ...Array.from(new Set(stockItems.map(item => item.category)))];

  const filteredItems = stockItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const lowStockItems = stockItems.filter(item => item.currentStock <= item.minStock);

  const getStockStatus = (current: number, min: number, max: number) => {
    if (current <= min) return { label: 'Low Stock', variant: 'destructive' as const };
    if (current >= max * 0.8) return { label: 'High Stock', variant: 'default' as const };
    return { label: 'Normal', variant: 'secondary' as const };
  };

  const fetchMutations = async (id: string) => {
    setMutationsLoading(m => ({ ...m, [id]: true }));
    setMutationsError(e => ({ ...e, [id]: null }));
    try {
      const res = await fetch(`/api/stock/${id}/mutation`);
      if (!res.ok) throw new Error('Gagal mengambil mutasi');
      const data = await res.json();
      setMutations(m => ({ ...m, [id]: data }));
    } catch (err: any) {
      setMutationsError(e => ({ ...e, [id]: err.message || 'Gagal mengambil mutasi' }));
    } finally {
      setMutationsLoading(m => ({ ...m, [id]: false }));
    }
  };

  // Modal open handler
  const openAddModal = () => {
    setEditing(null);
    setForm({});
    setShowAddModal(true);
  };
  const openEditModal = (item: StockItem) => {
    setEditing(item);
    setForm({ ...item });
    setShowAddModal(true);
  };
  const openStockInModal = (item: StockItem) => {
    setStockInItem(item);
    setStockInAmount(0);
    setStockInTransactionName('');
    setStockInError(null);
    setShowStockInModal(true);
  };
  const openStockOutModal = (item: StockItem) => {
    setStockOutItem(item);
    setStockOutAmount(0);
    setStockOutTransactionName('');
    setStockOutError(null);
    setShowStockOutModal(true);
  };

  // Form change handler
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setForm(f => ({ ...f, [id]: value }));
  };

  // Form submit handler
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormLoading(true);
    setFormError(null);
    try {
      const payload = {
        name: form.name,
        category: form.category,
        currentStock: form.currentStock,
        minStock: form.minStock,
        maxStock: form.maxStock,
        unit: form.unit,
        cost: form.cost,
        supplier: form.supplier,
      };
      let res;
      if (editing) {
        res = await fetch(`/api/stock/${editing.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
      } else {
        res = await fetch('/api/stock', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
      }
      if (!res.ok) throw new Error('Gagal menyimpan data stok');
      // Refresh data
      const refreshed = await fetch('/api/stock').then(r => r.json());
      setStockItems(refreshed || []);
      setShowAddModal(false);
    } catch (err: any) {
      setFormError(err.message || 'Gagal menyimpan data stok');
    } finally {
      setFormLoading(false);
    }
  };

  const handleStockIn = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!stockInItem) return;
    setStockInLoading(true);
    setStockInError(null);
    try {
      const userStr = localStorage.getItem('user');
      const user = userStr ? JSON.parse(userStr) : { username: 'unknown' };
      const res = await fetch(`/api/stock/${stockInItem.id}/mutation`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'IN',
          amount: stockInAmount,
          user: user.username || 'unknown',
          transactionName: stockInTransactionName || null
        }),
      });
      if (!res.ok) throw new Error('Gagal menambah stok');
      // Refresh data
      const refreshed = await fetch('/api/stock').then(r => r.json());
      setStockItems(refreshed || []);
      if (expandedItemId === stockInItem.id) fetchMutations(stockInItem.id);
      setShowStockInModal(false);
    } catch (err: any) {
      setStockInError(err.message || 'Gagal menambah stok');
    } finally {
      setStockInLoading(false);
    }
  };

  const handleStockOut = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!stockOutItem) return;
    setStockOutLoading(true);
    setStockOutError(null);
    try {
      const userStr = localStorage.getItem('user');
      const user = userStr ? JSON.parse(userStr) : { username: 'unknown' };
      const res = await fetch(`/api/stock/${stockOutItem.id}/mutation`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'OUT',
          amount: stockOutAmount,
          user: user.username || 'unknown',
          transactionName: stockOutTransactionName || null
        }),
      });
      if (!res.ok) throw new Error('Gagal mengurangi stok');
      // Refresh data
      const refreshed = await fetch('/api/stock').then(r => r.json());
      setStockItems(refreshed || []);
      if (expandedItemId === stockOutItem.id) fetchMutations(stockOutItem.id);
      setShowStockOutModal(false);
    } catch (err: any) {
      setStockOutError(err.message || 'Gagal mengurangi stok');
    } finally {
      setStockOutLoading(false);
    }
  };

  // Mutation CRUD handlers
  const openViewMutationModal = (mutation: any) => {
    setEditingMutation(mutation);
    setShowViewMutationModal(true);
  };

  const openEditMutationModal = (mutation: any) => {
    setEditingMutation(mutation);
    setMutationForm({
      type: mutation.type,
      amount: mutation.amount,
      user: mutation.user,
      transactionName: mutation.transactionName || ''
    });
    setMutationFormError(null);
    setShowEditMutationModal(true);
  };

  const handleMutationFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setMutationForm((f: any) => ({ ...f, [id]: value }));
  };

  const handleUpdateMutation = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingMutation) return;

    setMutationFormLoading(true);
    setMutationFormError(null);

    try {
      const res = await fetch(`/api/stock-mutation/${editingMutation.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(mutationForm),
      });

      if (!res.ok) throw new Error('Gagal mengupdate mutasi');

      // Refresh data
      const refreshed = await fetch('/api/stock').then(r => r.json());
      setStockItems(refreshed || []);
      if (expandedItemId) fetchMutations(expandedItemId);

      setShowEditMutationModal(false);
    } catch (err: any) {
      setMutationFormError(err.message || 'Gagal mengupdate mutasi');
    } finally {
      setMutationFormLoading(false);
    }
  };

  const handleDeleteMutation = async (mutationId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus mutasi ini?')) return;

    try {
      const res = await fetch(`/api/stock-mutation/${mutationId}`, {
        method: 'DELETE',
      });

      if (!res.ok) throw new Error('Gagal menghapus mutasi');

      // Refresh data
      const refreshed = await fetch('/api/stock').then(r => r.json());
      setStockItems(refreshed || []);
      if (expandedItemId) fetchMutations(expandedItemId);
    } catch (err: any) {
      alert(err.message || 'Gagal menghapus mutasi');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Data Stok</h1>
            <p className="text-muted-foreground">Kelola inventori dan stok barang</p>
          </div>
          
          <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
            <DialogTrigger asChild>
              <Button onClick={openAddModal}>
                <Plus className="mr-2 h-4 w-4" />
                Tambah Stok
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editing ? 'Edit Item Stok' : 'Tambah Item Stok'}</DialogTitle>
              </DialogHeader>
              <form className="space-y-4" onSubmit={handleFormSubmit}>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Nama Item</Label>
                    <Input id="name" value={form.name || ''} onChange={handleFormChange} placeholder="Masukkan nama item" required />
                  </div>
                  <div>
                    <Label htmlFor="category">Kategori</Label>
                    <select id="category" value={form.category || ''} onChange={handleFormChange} className="w-full p-2 border rounded-md" required>
                      <option value="">Pilih kategori</option>
                      <option value="Bahan Pokok">Bahan Pokok</option>
                      <option value="Protein">Protein</option>
                      <option value="Sayuran">Sayuran</option>
                      <option value="Minyak">Minyak</option>
                      <option value="Bumbu">Bumbu</option>
                    </select>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="currentStock">Stok Saat Ini</Label>
                    <Input id="currentStock" type="number" value={form.currentStock || ''} onChange={handleFormChange} placeholder="0" required />
                  </div>
                  <div>
                    <Label htmlFor="minStock">Stok Minimum</Label>
                    <Input id="minStock" type="number" value={form.minStock || ''} onChange={handleFormChange} placeholder="0" required />
                  </div>
                  <div>
                    <Label htmlFor="maxStock">Stok Maksimum</Label>
                    <Input id="maxStock" type="number" value={form.maxStock || ''} onChange={handleFormChange} placeholder="0" required />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="unit">Satuan</Label>
                    <Input id="unit" value={form.unit || ''} onChange={handleFormChange} placeholder="kg, liter, pcs" required />
                  </div>
                  <div>
                    <Label htmlFor="cost">Harga/Unit</Label>
                    <Input id="cost" type="number" value={form.cost || ''} onChange={handleFormChange} placeholder="0" required />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="supplier">Supplier</Label>
                  <Input id="supplier" value={form.supplier || ''} onChange={handleFormChange} placeholder="Nama supplier" />
                </div>
                {formError && <div className="text-red-500 text-sm">{formError}</div>}
                <Button className="w-full" type="submit" disabled={formLoading}>{formLoading ? 'Menyimpan...' : 'Simpan'}</Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Items</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stockItems.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">{lowStockItems.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <TrendingUp className="h-4 w-4 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Rp {stockItems.reduce((sum, item) => sum + (item.currentStock * item.cost), 0).toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Categories</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{categories.length - 1}</div>
            </CardContent>
          </Card>
        </div>

        {/* Low Stock Alert */}
        {lowStockItems.length > 0 && (
          <Card className="mb-6 border-destructive">
            <CardHeader>
              <CardTitle className="text-destructive flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5" />
                Peringatan Stok Rendah
              </CardTitle>
              <CardDescription>
                {lowStockItems.length} item memiliki stok di bawah minimum
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {lowStockItems.map(item => (
                  <div key={item.id} className="flex justify-between items-center p-2 bg-destructive/10 rounded">
                    <span className="font-medium">{item.name}</span>
                    <span className="text-sm text-muted-foreground">
                      {item.currentStock} {item.unit} (min: {item.minStock})
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Cari Item</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Cari berdasarkan nama item..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="category">Kategori</Label>
                <select 
                  id="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  {categories.map(cat => (
                    <option key={cat} value={cat}>
                      {cat === 'all' ? 'Semua Kategori' : cat}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stock Table */}
        <Card>
          <CardHeader>
            <CardTitle>Daftar Stok</CardTitle>
            <CardDescription>
              Menampilkan {filteredItems.length} dari {stockItems.length} item
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nama Item</TableHead>
                  <TableHead>Kategori</TableHead>
                  <TableHead>Stok Saat Ini</TableHead>
                  <TableHead>Min/Max</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Update Terakhir</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredItems.map((item) => (
                  <StockRow
                    key={item.id}
                    item={item}
                    expandedItemId={expandedItemId}
                    setExpandedItemId={setExpandedItemId}
                    mutations={mutations}
                    mutationsLoading={mutationsLoading}
                    mutationsError={mutationsError}
                    fetchMutations={fetchMutations}
                    openEditModal={openEditModal}
                    openStockInModal={openStockInModal}
                    openStockOutModal={openStockOutModal}
                    getStockStatus={getStockStatus}
                    openViewMutationModal={openViewMutationModal}
                    openEditMutationModal={openEditMutationModal}
                    handleDeleteMutation={handleDeleteMutation}
                  />
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <Dialog open={showStockInModal} onOpenChange={setShowStockInModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Stock In - {stockInItem?.name}</DialogTitle>
          </DialogHeader>
          <form className="space-y-4" onSubmit={handleStockIn}>
            <div>
              <Label htmlFor="stockInAmount">Jumlah Penambahan Stok</Label>
              <Input id="stockInAmount" type="number" min={1} value={stockInAmount} onChange={e => setStockInAmount(Number(e.target.value))} required />
            </div>
            <div>
              <Label htmlFor="stockInTransactionName">Nama Transaksi (Opsional)</Label>
              <Input
                id="stockInTransactionName"
                type="text"
                value={stockInTransactionName}
                onChange={e => setStockInTransactionName(e.target.value)}
                placeholder="Contoh: Pembelian dari supplier, Restok bulanan, dll"
              />
            </div>
            {stockInError && <div className="text-red-500 text-sm">{stockInError}</div>}
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowStockInModal(false)}>Batal</Button>
              <Button type="submit" disabled={stockInLoading}>{stockInLoading ? 'Menyimpan...' : 'Simpan'}</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={showStockOutModal} onOpenChange={setShowStockOutModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Stock Out - {stockOutItem?.name}</DialogTitle>
          </DialogHeader>
          <form className="space-y-4" onSubmit={handleStockOut}>
            <div>
              <Label htmlFor="stockOutAmount">Jumlah Pengurangan Stok</Label>
              <Input id="stockOutAmount" type="number" min={1} max={stockOutItem?.currentStock ?? 1} value={stockOutAmount} onChange={e => setStockOutAmount(Number(e.target.value))} required />
            </div>
            <div>
              <Label htmlFor="stockOutTransactionName">Nama Transaksi (Opsional)</Label>
              <Input
                id="stockOutTransactionName"
                type="text"
                value={stockOutTransactionName}
                onChange={e => setStockOutTransactionName(e.target.value)}
                placeholder="Contoh: Penjualan, Rusak/expired, Konsumsi internal, dll"
              />
            </div>
            {stockOutError && <div className="text-red-500 text-sm">{stockOutError}</div>}
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowStockOutModal(false)}>Batal</Button>
              <Button type="submit" disabled={stockOutLoading}>{stockOutLoading ? 'Menyimpan...' : 'Simpan'}</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Mutation Modal */}
      <Dialog open={showViewMutationModal} onOpenChange={setShowViewMutationModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Detail Mutasi Stok</DialogTitle>
          </DialogHeader>
          {editingMutation && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Item Stok</Label>
                  <div className="p-2 bg-gray-50 rounded">{editingMutation.stockItem?.name || 'N/A'}</div>
                </div>
                <div>
                  <Label>Tipe Mutasi</Label>
                  <div className="p-2">
                    <Badge variant={editingMutation.type === 'IN' ? 'default' : 'destructive'}>
                      {editingMutation.type === 'IN' ? 'Stok Masuk' : 'Stok Keluar'}
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Jumlah</Label>
                  <div className="p-2 bg-gray-50 rounded">
                    <span className={editingMutation.type === 'IN' ? 'text-green-600' : 'text-red-600'}>
                      {editingMutation.type === 'IN' ? '+' : '-'}{editingMutation.amount} {editingMutation.stockItem?.unit || ''}
                    </span>
                  </div>
                </div>
                <div>
                  <Label>User</Label>
                  <div className="p-2 bg-gray-50 rounded">{editingMutation.user}</div>
                </div>
              </div>
              <div>
                <Label>Nama Transaksi</Label>
                <div className="p-2 bg-gray-50 rounded">
                  {editingMutation.transactionName || '-'}
                </div>
              </div>
              <div>
                <Label>Tanggal & Waktu</Label>
                <div className="p-2 bg-gray-50 rounded">
                  {new Date(editingMutation.createdAt).toLocaleString('id-ID')}
                </div>
              </div>
              <div className="flex justify-end">
                <Button variant="outline" onClick={() => setShowViewMutationModal(false)}>
                  Tutup
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Mutation Modal */}
      <Dialog open={showEditMutationModal} onOpenChange={setShowEditMutationModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Mutasi Stok</DialogTitle>
          </DialogHeader>
          <form className="space-y-4" onSubmit={handleUpdateMutation}>
            <div>
              <Label>Item Stok</Label>
              <div className="p-2 bg-gray-50 rounded text-gray-600">
                {editingMutation?.stockItem?.name || 'N/A'}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">Tipe Mutasi</Label>
                <select
                  id="type"
                  value={mutationForm.type || ''}
                  onChange={handleMutationFormChange}
                  className="w-full p-2 border rounded-md"
                  required
                >
                  <option value="IN">Stok Masuk</option>
                  <option value="OUT">Stok Keluar</option>
                </select>
              </div>
              <div>
                <Label htmlFor="amount">Jumlah</Label>
                <Input
                  id="amount"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={mutationForm.amount || ''}
                  onChange={handleMutationFormChange}
                  required
                />
              </div>
            </div>
            <div>
              <Label htmlFor="user">User</Label>
              <Input
                id="user"
                value={mutationForm.user || ''}
                onChange={handleMutationFormChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="transactionName">Nama Transaksi (Opsional)</Label>
              <Input
                id="transactionName"
                value={mutationForm.transactionName || ''}
                onChange={handleMutationFormChange}
                placeholder="Masukkan nama transaksi"
              />
            </div>
            {mutationFormError && (
              <div className="text-red-500 text-sm">{mutationFormError}</div>
            )}
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowEditMutationModal(false)}
              >
                Batal
              </Button>
              <Button type="submit" disabled={mutationFormLoading}>
                {mutationFormLoading ? 'Menyimpan...' : 'Simpan'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Stock;
