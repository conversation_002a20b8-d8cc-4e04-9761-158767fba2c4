
import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Plus, Search, Edit, Trash2, Image as ImageIcon } from 'lucide-react';
import { Navigation } from '@/components/Navigation';
import MenuFormModal from '../components/MenuFormModal';
import ConfirmDialog from '../components/ConfirmDialog';
import SmartDeleteDialog from '../components/SmartDeleteDialog';
import * as XLSX from 'xlsx';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  available: boolean;
  preparationTime: number;
  ingredients: string[];
}

interface StockItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  lastUpdated: string;
  supplier: string;
  cost: number;
}

const Menu = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [editingMenu, setEditingMenu] = useState<MenuItem | null>(null);
  const [deletingMenu, setDeletingMenu] = useState<MenuItem | null>(null);
  const [canDeleteMenu, setCanDeleteMenu] = useState<boolean>(true);
  const [hasOrdersMenu, setHasOrdersMenu] = useState<boolean>(false);
  const [stockItems, setStockItems] = useState<StockItem[]>([]);
  const [categories, setCategories] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch menu items, stock items, and categories in parallel
        const [menuRes, stockRes, categoriesRes] = await Promise.all([
          fetch('/api/menu-items'),
          fetch('/api/stock'),
          fetch('/api/categories')
        ]);

        if (!menuRes.ok) throw new Error('Gagal mengambil data menu');
        if (!stockRes.ok) throw new Error('Gagal mengambil data stok');
        if (!categoriesRes.ok) throw new Error('Gagal mengambil data kategori');

        const [menuData, stockData, categoriesData] = await Promise.all([
          menuRes.json(),
          stockRes.json(),
          categoriesRes.json()
        ]);

        setMenuItems(menuData || []);
        setStockItems(stockData || []);
        setCategories(categoriesData || []);
        setLoading(false);
      } catch (err: any) {
        setError(err.message || 'Gagal mengambil data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const refreshMenu = async () => {
    setLoading(true);
    setError(null);
    try {
      // Refresh menu items, stock items, and categories
      const [menuRes, stockRes, categoriesRes] = await Promise.all([
        fetch('/api/menu-items'),
        fetch('/api/stock'),
        fetch('/api/categories')
      ]);

      if (!menuRes.ok) throw new Error('Gagal mengambil data menu');
      if (!stockRes.ok) throw new Error('Gagal mengambil data stok');
      if (!categoriesRes.ok) throw new Error('Gagal mengambil data kategori');

      const [menuData, stockData, categoriesData] = await Promise.all([
        menuRes.json(),
        stockRes.json(),
        categoriesRes.json()
      ]);

      setMenuItems(menuData || []);
      setStockItems(stockData || []);
      setCategories(categoriesData || []);
    } catch (err: any) {
      setError(err.message || 'Gagal mengambil data');
    } finally {
      setLoading(false);
    }
  };

  const handleAddMenu = () => {
    setEditingMenu(null);
    setShowAddModal(true);
  };

  const handleEditMenu = (item: MenuItem) => {
    setEditingMenu(item);
    setShowAddModal(true);
  };

  const handleDeleteMenu = async (item: MenuItem) => {
    setDeletingMenu(item);

    // Check if menu can be deleted by making a test request
    try {
      const res = await fetch(`/api/menu-items/${item.id}`, {
        method: 'DELETE',
        headers: { 'X-Test-Delete': 'true' } // Add header to indicate test
      });

      if (res.status === 400) {
        const errorData = await res.json().catch(() => ({}));
        setCanDeleteMenu(false);
        setHasOrdersMenu(!!errorData.hasOrders);
      } else {
        setCanDeleteMenu(true);
        setHasOrdersMenu(false);
      }
    } catch (error) {
      // If test fails, assume it can be deleted and let the actual delete handle the error
      setCanDeleteMenu(true);
      setHasOrdersMenu(false);
    }
  };

  const handleToggleAvailability = async (item: MenuItem) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/menu-items/${item.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...item,
          available: !item.available
        }),
      });

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.error || 'Gagal mengubah status menu');
      }

      await refreshMenu(); // Refresh data
      alert(`Menu ${item.available ? 'dinonaktifkan' : 'diaktifkan'} berhasil!`);

    } catch (err: any) {
      console.error('Toggle availability error:', err);
      alert(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleMenuFormSubmit = async (data: Partial<MenuItem>) => {
    setFormLoading(true);
    setFormError(null);
    try {
      let res;

      // Find categoryId from category name
      const selectedCategory = categories.find(cat => cat.name === data.category);
      const categoryId = selectedCategory ? selectedCategory.id : null;

      if (!categoryId) {
        throw new Error('Kategori tidak valid');
      }

      const payload = {
        name: data.name,
        description: data.description,
        price: data.price,
        categoryId: categoryId,
        image: data.image,
        available: data.available,
        preparationTime: data.preparationTime,
        ingredients: data.ingredients,
      };
      if (editingMenu) {
        res = await fetch(`/api/menu-items/${editingMenu.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
      } else {
        res = await fetch('/api/menu-items', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
      }
      if (!res.ok) throw new Error('Gagal menyimpan data menu');
      await refreshMenu();
      setShowAddModal(false);
      setEditingMenu(null);
    } catch (err: any) {
      setFormError(err.message || 'Gagal menyimpan data menu');
    } finally {
      setFormLoading(false);
    }
  };

  const handleConfirmDelete = async () => {
    if (!deletingMenu) return;
    setDeleteLoading(true);
    setDeleteError(null);
    try {
      const res = await fetch(`/api/menu-items/${deletingMenu.id}`, { method: 'DELETE' });

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.error || `Error ${res.status}: Gagal menghapus menu`);
      }

      await refreshMenu(); // Refresh data
      setDeletingMenu(null);

      // Show success message
      alert('Menu berhasil dihapus!');

    } catch (err: any) {
      console.error('Delete error:', err);
      setDeleteError(err.message || 'Gagal menghapus menu');
      alert(`Error: ${err.message}`);
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleDisableMenu = async () => {
    if (!deletingMenu) return;
    await handleToggleAvailability(deletingMenu);
    setDeletingMenu(null);
  };

  const handleImportExcel = () => {
    setShowImportModal(true);
  };

  const handleDownloadTemplate = () => {
    // Header sesuai dengan struktur database
    const headers = [
      'Nama Menu',
      'Deskripsi',
      'Harga',
      'Kategori',
      'Waktu Persiapan (menit)',
      'Status (Tersedia/Tidak Tersedia)',
      'Bahan (JSON format atau pisahkan dengan koma)'
    ];

    // Contoh data untuk panduan user
    const exampleData = [
      [
        'Nasi Goreng Spesial',
        'Nasi goreng dengan telur, ayam, dan sayuran segar',
        25000,
        'Makanan Utama',
        15,
        'Tersedia',
        'Nasi, Telur, Ayam, Bawang, Kecap'
      ],
      [
        'Es Teh Manis',
        'Teh manis dingin segar',
        5000,
        'Minuman',
        5,
        'Tersedia',
        'Teh, Gula, Es'
      ],
      [
        'Ayam Bakar',
        'Ayam bakar bumbu khas dengan nasi putih',
        35000,
        'Makanan Utama',
        30,
        'Tersedia',
        'Ayam, Bumbu Bakar, Nasi'
      ]
    ];

    const ws = XLSX.utils.aoa_to_sheet([headers, ...exampleData]);

    // Set column widths
    const colWidths = [
      { wch: 20 }, // Nama Menu
      { wch: 40 }, // Deskripsi
      { wch: 10 }, // Harga
      { wch: 15 }, // Kategori
      { wch: 20 }, // Waktu Persiapan
      { wch: 25 }, // Status
      { wch: 40 }  // Bahan
    ];
    ws['!cols'] = colWidths;

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Template Menu');
    XLSX.writeFile(wb, 'Template_Import_Menu.xlsx');
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setLoading(true);
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const json: any[] = XLSX.utils.sheet_to_json(worksheet, { defval: '' });

      // Validasi dan mapping data Excel ke format database
      const importedMenus = [];
      const errors = [];

      for (let i = 0; i < json.length; i++) {
        const row = json[i];
        const rowNum = i + 2; // +2 karena baris 1 adalah header

        try {
          // Validasi required fields
          if (!row['Nama Menu']) {
            errors.push(`Baris ${rowNum}: Nama Menu wajib diisi`);
            continue;
          }

          if (!row['Harga'] || isNaN(Number(row['Harga']))) {
            errors.push(`Baris ${rowNum}: Harga harus berupa angka`);
            continue;
          }

          if (!row['Kategori']) {
            errors.push(`Baris ${rowNum}: Kategori wajib diisi`);
            continue;
          }

          // Cari kategori yang sesuai
          const categoryName = row['Kategori'].toString().trim();
          const category = categories.find(cat =>
            cat.name.toLowerCase() === categoryName.toLowerCase()
          );

          if (!category) {
            errors.push(`Baris ${rowNum}: Kategori "${categoryName}" tidak ditemukan. Kategori yang tersedia: ${categories.map(c => c.name).join(', ')}`);
            continue;
          }

          // Parse ingredients
          let ingredients = [];
          if (row['Bahan (JSON format atau pisahkan dengan koma)']) {
            const bahanStr = row['Bahan (JSON format atau pisahkan dengan koma)'].toString();
            try {
              // Coba parse sebagai JSON dulu
              ingredients = JSON.parse(bahanStr);
            } catch {
              // Jika gagal, split by comma
              ingredients = bahanStr.split(',').map((s: string) => s.trim()).filter(Boolean);
            }
          }

          // Parse status
          const statusStr = row['Status (Tersedia/Tidak Tersedia)']?.toString().toLowerCase() || 'tersedia';
          const available = statusStr.includes('tersedia');

          // Parse preparation time
          const prepTimeStr = row['Waktu Persiapan (menit)'];
          const preparationTime = prepTimeStr ? parseInt(prepTimeStr.toString()) : null;

          const menuData = {
            name: row['Nama Menu'].toString().trim(),
            description: row['Deskripsi']?.toString().trim() || '',
            price: parseFloat(row['Harga'].toString()),
            categoryId: category.id,
            available,
            preparationTime,
            ingredients: ingredients.length > 0 ? JSON.stringify(ingredients) : null
          };

          // Kirim ke API
          const response = await fetch('/api/menu-items', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(menuData),
          });

          if (!response.ok) {
            const errorData = await response.json();
            errors.push(`Baris ${rowNum}: ${errorData.error || 'Gagal menyimpan menu'}`);
          } else {
            const newMenuItem = await response.json();
            importedMenus.push(newMenuItem);
          }

        } catch (error: any) {
          errors.push(`Baris ${rowNum}: ${error.message}`);
        }
      }

      // Tampilkan hasil
      if (errors.length > 0) {
        alert(`Import selesai dengan ${errors.length} error:\n\n${errors.join('\n')}\n\n${importedMenus.length} menu berhasil diimport.`);
      } else {
        alert(`Import berhasil! ${importedMenus.length} menu telah ditambahkan.`);
      }

      // Refresh data
      await refreshMenu();
      setShowImportModal(false);
      if (fileInputRef.current) fileInputRef.current.value = '';

    } catch (error: any) {
      alert(`Error saat import: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Get category names for filtering
  const categoryNames = ['all', ...categories.map(cat => cat.name)];

  // Helper function to parse ingredients
  const parseIngredients = (ingredients: any): any[] => {
    if (!ingredients) return [];

    try {
      if (typeof ingredients === 'string') {
        // Try to parse as JSON first
        try {
          const parsed = JSON.parse(ingredients);
          return Array.isArray(parsed) ? parsed : [];
        } catch {
          // If JSON parsing fails, split by comma
          return ingredients.split(',').map((s: string) => s.trim()).filter(Boolean);
        }
      } else if (Array.isArray(ingredients)) {
        return ingredients;
      }
    } catch (error) {
      console.error('Error parsing ingredients:', error);
    }

    return [];
  };

  // Helper function to check menu availability based on stock
  const getMenuAvailability = (item: MenuItem) => {
    if (!item.available) return { canMake: false, reason: 'Menu tidak tersedia', minPortions: 0 };

    let canMake = true;
    let hasLowStock = false;
    let minPortions = Infinity;
    let unavailableIngredients: string[] = [];

    const ingredientsArray = parseIngredients(item.ingredients);

    if (Array.isArray(ingredientsArray) && ingredientsArray.length > 0) {
      ingredientsArray.forEach((bahan) => {
        let bahanName, bahanQty;
        if (typeof bahan === 'string') {
          bahanName = bahan;
          bahanQty = 1;
        } else {
          bahanName = bahan.name;
          bahanQty = bahan.qty || 1;
        }

        const stock = stockItems.find(s => s.name.toLowerCase() === bahanName.toLowerCase());
        if (stock) {
          const possiblePortions = Math.floor(stock.currentStock / bahanQty);
          minPortions = Math.min(minPortions, possiblePortions);

          if (stock.currentStock === 0) {
            canMake = false;
            unavailableIngredients.push(bahanName);
          } else if (stock.currentStock <= stock.minStock) {
            hasLowStock = true;
          }
        }
      });
    }

    return {
      canMake,
      hasLowStock,
      minPortions: minPortions === Infinity ? 999 : minPortions,
      unavailableIngredients,
      reason: !canMake ? `Stok habis: ${unavailableIngredients.join(', ')}` : hasLowStock ? 'Stok rendah' : 'Tersedia'
    };
  };

  const filteredItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const categoryName = typeof item.category === 'object' && item.category !== null ? item.category.name : item.category;
    const matchesCategory = selectedCategory === 'all' || categoryName === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Kelola Menu</h1>
            <p className="text-muted-foreground">Atur menu dan harga makanan</p>
          </div>
          <div className="flex gap-2">
            <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
              <DialogTrigger asChild>
                <Button onClick={handleAddMenu}>
                  <Plus className="mr-2 h-4 w-4" />
                  Tambah Menu
                </Button>
              </DialogTrigger>
              <MenuFormModal
                open={showAddModal}
                onClose={() => { setShowAddModal(false); setEditingMenu(null); }}
                onSubmit={handleMenuFormSubmit}
                initialData={editingMenu}
                categories={categories.map(cat => cat.name)}
                stockItems={stockItems}
              />
            </Dialog>
            <Button variant="outline" onClick={handleImportExcel}>
              Import Excel
            </Button>
            <Button variant="ghost" onClick={handleDownloadTemplate}>
              Download Template
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Menu</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{menuItems.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tersedia</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">
                {menuItems.filter(item => item.available).length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tidak Tersedia</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">
                {menuItems.filter(item => !item.available).length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rata-rata Harga</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                Rp {Math.round(menuItems.reduce((sum, item) => sum + item.price, 0) / menuItems.length).toLocaleString()}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Cari Menu</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Cari berdasarkan nama menu..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="category">Kategori</Label>
                <select 
                  id="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  {categoryNames.map(cat => (
                    <option key={cat} value={cat}>
                      {cat === 'all' ? 'Semua Kategori' : cat}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Menu Table */}
        <Card>
          <CardHeader>
            <CardTitle>Daftar Menu</CardTitle>
            <CardDescription>
              Menampilkan {filteredItems.length} dari {menuItems.length} menu
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-12 text-gray-500">Memuat data menu...</div>
            ) : error ? (
              <div className="text-center py-12 text-red-500">{error}</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Gambar</TableHead>
                    <TableHead>Nama Menu</TableHead>
                    <TableHead>Kategori</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Waktu Persiapan</TableHead>
                    <TableHead>Bahan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="w-12 h-12 bg-muted rounded flex items-center justify-center">
                          <ImageIcon className="h-6 w-6 text-muted-foreground" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{item.name}</div>
                          <div className="text-sm text-muted-foreground">{item.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>{typeof item.category === 'object' && item.category !== null ? item.category.name : item.category}</TableCell>
                      <TableCell>Rp {item.price.toLocaleString()}</TableCell>
                      <TableCell>{item.preparationTime} menit</TableCell>
                      <TableCell>
                        <ul className="text-xs text-muted-foreground space-y-1">
                          {(() => {
                            const ingredientsArray = parseIngredients(item.ingredients);

                            return Array.isArray(ingredientsArray) && ingredientsArray.length > 0 ? (
                              ingredientsArray.map((bahan, idx) => {
                                // Handle both string and object format
                                let bahanName, bahanQty, bahanUnit;
                                if (typeof bahan === 'string') {
                                  bahanName = bahan;
                                  bahanQty = 1;
                                  bahanUnit = '';
                                } else {
                                  bahanName = bahan.name;
                                  bahanQty = bahan.qty || 1;
                                  bahanUnit = bahan.unit || '';
                                }

                                const stock = stockItems.find(s => s.name.toLowerCase() === bahanName.toLowerCase());
                                const isLowStock = stock && stock.currentStock <= stock.minStock;
                                const isOutOfStock = stock && stock.currentStock === 0;

                                return (
                                  <li key={idx} className="flex justify-between items-center">
                                    <span>{bahanName} ({bahanQty} {bahanUnit || (stock?.unit || '')})</span>
                                    {stock && (
                                      <span className={`text-xs px-1 py-0.5 rounded ${
                                        isOutOfStock ? 'bg-red-100 text-red-700' :
                                        isLowStock ? 'bg-yellow-100 text-yellow-700' :
                                        'bg-green-100 text-green-700'
                                      }`}>
                                        {stock.currentStock} {stock.unit}
                                      </span>
                                    )}
                                  </li>
                                );
                              })
                            ) : (
                              <li>-</li>
                            );
                          })()}
                        </ul>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const availability = getMenuAvailability(item);

                          if (!item.available) {
                            return <Badge variant="secondary">Tidak Tersedia</Badge>;
                          } else if (!availability.canMake) {
                            return (
                              <div className="space-y-1">
                                <Badge variant="destructive">Stok Habis</Badge>
                                <div className="text-xs text-red-600" title={availability.reason}>
                                  {availability.unavailableIngredients.join(', ')}
                                </div>
                              </div>
                            );
                          } else if (availability.hasLowStock) {
                            return (
                              <div className="space-y-1">
                                <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                                  Stok Rendah
                                </Badge>
                                <div className="text-xs text-muted-foreground">
                                  Dapat buat: {availability.minPortions} porsi
                                </div>
                              </div>
                            );
                          } else {
                            return (
                              <div className="space-y-1">
                                <Badge variant="default">Tersedia</Badge>
                                <div className="text-xs text-muted-foreground">
                                  Dapat buat: {availability.minPortions} porsi
                                </div>
                              </div>
                            );
                          }
                        })()}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" onClick={() => handleEditMenu(item)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant={item.available ? "outline" : "default"}
                            onClick={() => handleToggleAvailability(item)}
                            title={item.available ? "Nonaktifkan menu" : "Aktifkan menu"}
                          >
                            {item.available ? "🔴" : "🟢"}
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleDeleteMenu(item)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
        <SmartDeleteDialog
          open={!!deletingMenu}
          onClose={() => setDeletingMenu(null)}
          onDelete={handleConfirmDelete}
          onDisable={handleDisableMenu}
          title={canDeleteMenu ? "Hapus Menu?" : "Menu Tidak Dapat Dihapus"}
          description={canDeleteMenu ? undefined : "Menu ini tidak dapat dihapus karena sudah pernah dipesan"}
          itemName={deletingMenu?.name || ''}
          canDelete={canDeleteMenu}
          hasOrders={hasOrdersMenu}
        />
      </div>
      {/* Import Excel Modal */}
      <Dialog open={showImportModal} onOpenChange={setShowImportModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Menu dari Excel</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
            <div className="space-y-2">
              <p className="text-sm font-medium">Format kolom yang diperlukan:</p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• <strong>Nama Menu</strong> - Wajib diisi</li>
                <li>• <strong>Deskripsi</strong> - Opsional</li>
                <li>• <strong>Harga</strong> - Wajib diisi (angka)</li>
                <li>• <strong>Kategori</strong> - Wajib diisi (harus sesuai kategori yang ada)</li>
                <li>• <strong>Waktu Persiapan (menit)</strong> - Opsional (angka)</li>
                <li>• <strong>Status (Tersedia/Tidak Tersedia)</strong> - Default: Tersedia</li>
                <li>• <strong>Bahan (JSON format atau pisahkan dengan koma)</strong> - Opsional</li>
              </ul>
              <p className="text-xs text-blue-600">
                💡 Tip: Download template terlebih dahulu untuk melihat format yang benar
              </p>
              <p className="text-xs text-orange-600">
                ⚠️ Kategori yang tersedia: {categories.map(c => c.name).join(', ')}
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Menu;
