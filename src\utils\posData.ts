
import { Table, MenuItem, MenuCategory } from '../types/pos';

export const mockTables: Table[] = [
  { id: '1', number: 1, seats: 4, status: 'available' },
  { id: '2', number: 2, seats: 2, status: 'occupied' },
  { id: '3', number: 3, seats: 6, status: 'reserved', reservationTime: '19:00' },
  { id: '4', number: 4, seats: 4, status: 'available' },
  { id: '5', number: 5, seats: 8, status: 'occupied' },
  { id: '6', number: 6, seats: 2, status: 'available' },
  { id: '7', number: 7, seats: 4, status: 'available' },
  { id: '8', number: 8, seats: 6, status: 'reserved', reservationTime: '20:30' },
  { id: '9', number: 9, seats: 4, status: 'available' },
  { id: '10', number: 10, seats: 2, status: 'occupied' },
  { id: '11', number: 11, seats: 4, status: 'available' },
  { id: '12', number: 12, seats: 6, status: 'available' },
];

export const menuCategories: MenuCategory[] = [
  { id: '1', name: '<PERSON><PERSON><PERSON>', icon: '🍽️', color: 'bg-red-500' },
  { id: '2', name: 'Minuman', icon: '🥤', color: 'bg-blue-500' },
  { id: '3', name: 'Dessert', icon: '🍰', color: 'bg-pink-500' },
  { id: '4', name: 'Appetizer', icon: '🥗', color: 'bg-green-500' },
];

export const mockMenuItems: MenuItem[] = [
  {
    id: '1',
    name: 'Nasi Goreng Spesial',
    price: 25000,
    category: 'Makanan Utama',
    description: 'Nasi goreng dengan telur, ayam, dan sayuran segar',
    available: true,
  },
  {
    id: '2',
    name: 'Ayam Bakar',
    price: 35000,
    category: 'Makanan Utama',
    description: 'Ayam bakar dengan bumbu khas dan nasi putih',
    available: true,
  },
  {
    id: '3',
    name: 'Gado-Gado',
    price: 20000,
    category: 'Makanan Utama',
    description: 'Gado-gado dengan sayuran segar dan bumbu kacang',
    available: true,
  },
  {
    id: '4',
    name: 'Es Teh Manis',
    price: 8000,
    category: 'Minuman',
    description: 'Es teh manis segar',
    available: true,
  },
  {
    id: '5',
    name: 'Jus Jeruk',
    price: 15000,
    category: 'Minuman',
    description: 'Jus jeruk segar tanpa pemanis buatan',
    available: true,
  },
  {
    id: '6',
    name: 'Kopi Hitam',
    price: 12000,
    category: 'Minuman',
    description: 'Kopi hitam robusta pilihan',
    available: true,
  },
  {
    id: '7',
    name: 'Es Krim Vanilla',
    price: 18000,
    category: 'Dessert',
    description: 'Es krim vanilla dengan topping cokelat',
    available: true,
  },
  {
    id: '8',
    name: 'Pisang Goreng',
    price: 15000,
    category: 'Dessert',
    description: 'Pisang goreng crispy dengan madu',
    available: true,
  },
  {
    id: '9',
    name: 'Kerupuk Udang',
    price: 10000,
    category: 'Appetizer',
    description: 'Kerupuk udang renyah',
    available: true,
  },
  {
    id: '10',
    name: 'Lumpia Semarang',
    price: 12000,
    category: 'Appetizer',
    description: 'Lumpia dengan isian sayuran dan daging',
    available: true,
  },
];

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount);
};

export const getTableStatusColor = (status: Table['status']): string => {
  switch (status) {
    case 'available':
      return 'border-green-300 bg-green-50';
    case 'occupied':
      return 'border-red-300 bg-red-50';
    case 'reserved':
      return 'border-yellow-300 bg-yellow-50';
    default:
      return 'border-gray-300 bg-gray-50';
  }
};

export const getTableStatusText = (status: Table['status']): string => {
  switch (status) {
    case 'available':
      return 'Tersedia';
    case 'occupied':
      return 'Terisi';
    case 'reserved':
      return 'Reservasi';
    default:
      return 'Unknown';
  }
};
