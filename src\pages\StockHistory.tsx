import React, { useState, useEffect } from 'react';
import { Navigation } from '../components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Badge } from '../components/ui/badge';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import { Search, Download, Filter, TrendingUp, TrendingDown } from 'lucide-react';

interface StockMutation {
  id: string;
  type: string;
  amount: number;
  user: string;
  transactionName?: string;
  createdAt: string;
  stockItem: {
    name: string;
    unit: string;
    category: string;
  };
}

const StockHistory: React.FC = () => {
  const [mutations, setMutations] = useState<StockMutation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'ALL' | 'IN' | 'OUT'>('ALL');
  const [filterCategory, setFilterCategory] = useState('');

  useEffect(() => {
    fetchStockHistory();
  }, []);

  const fetchStockHistory = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/stock-history');
      if (!res.ok) throw new Error('Gagal mengambil history stok');
      const data = await res.json();
      setMutations(data);
    } catch (err: any) {
      setError(err.message || 'Gagal mengambil history stok');
    } finally {
      setLoading(false);
    }
  };

  // Filter mutations based on search and filters
  const filteredMutations = mutations.filter(mutation => {
    const matchesSearch = 
      mutation.stockItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mutation.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (mutation.transactionName && mutation.transactionName.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = filterType === 'ALL' || mutation.type === filterType;
    const matchesCategory = !filterCategory || mutation.stockItem.category === filterCategory;
    
    return matchesSearch && matchesType && matchesCategory;
  });

  // Get unique categories for filter
  const categories = Array.from(new Set(mutations.map(m => m.stockItem.category)));

  // Calculate statistics
  const totalIn = mutations.filter(m => m.type === 'IN').reduce((sum, m) => sum + m.amount, 0);
  const totalOut = mutations.filter(m => m.type === 'OUT').reduce((sum, m) => sum + m.amount, 0);
  const totalTransactions = mutations.length;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto py-8 px-4">
          <div className="flex justify-center items-center h-64">
            <div>Loading...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto py-8 px-4">
          <div className="flex justify-center items-center h-64">
            <div className="text-red-500">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">History Stok</h1>
            <p className="text-muted-foreground">Riwayat mutasi stok masuk dan keluar</p>
          </div>
          
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Transaksi</CardTitle>
              <Filter className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalTransactions}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Stok Masuk</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{totalIn.toFixed(2)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Stok Keluar</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{totalOut.toFixed(2)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Movement</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${(totalIn - totalOut) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {(totalIn - totalOut).toFixed(2)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filter & Pencarian</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari item, user, atau transaksi..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as 'ALL' | 'IN' | 'OUT')}
                className="w-full p-2 border rounded-md"
              >
                <option value="ALL">Semua Tipe</option>
                <option value="IN">Stok Masuk</option>
                <option value="OUT">Stok Keluar</option>
              </select>
              
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">Semua Kategori</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setFilterType('ALL');
                  setFilterCategory('');
                }}
              >
                Reset Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* History Table */}
        <Card>
          <CardHeader>
            <CardTitle>Riwayat Mutasi Stok</CardTitle>
            <CardDescription>
              Menampilkan {filteredMutations.length} dari {mutations.length} transaksi
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tanggal & Waktu</TableHead>
                  <TableHead>Item</TableHead>
                  <TableHead>Kategori</TableHead>
                  <TableHead>Tipe</TableHead>
                  <TableHead>Jumlah</TableHead>
                  <TableHead>Nama Transaksi</TableHead>
                  <TableHead>User</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMutations.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      Tidak ada data history stok
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredMutations.map((mutation) => (
                    <TableRow key={mutation.id}>
                      <TableCell className="text-sm">
                        {formatDate(mutation.createdAt)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {mutation.stockItem.name}
                      </TableCell>
                      <TableCell>
                        {mutation.stockItem.category}
                      </TableCell>
                      <TableCell>
                        <Badge variant={mutation.type === 'IN' ? 'default' : 'destructive'}>
                          {mutation.type === 'IN' ? 'Masuk' : 'Keluar'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className={mutation.type === 'IN' ? 'text-green-600' : 'text-red-600'}>
                          {mutation.type === 'IN' ? '+' : '-'}{mutation.amount} {mutation.stockItem.unit}
                        </span>
                      </TableCell>
                      <TableCell>
                        {mutation.transactionName || '-'}
                      </TableCell>
                      <TableCell>
                        {mutation.user}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StockHistory;
