import { useState, useEffect } from 'react';
import { Table } from '../types/pos';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Button } from './ui/button';

interface TableFormModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (table: Partial<Table>) => void;
  initialData?: Table | null;
  nextTableNumber?: number;
}

const TableFormModal = ({ open, onClose, onSubmit, initialData, nextTableNumber }: TableFormModalProps) => {
  const [form, setForm] = useState<any>({
    number: '',
    seats: '',
    status: 'available',
    reservationTime: ''
  });

  useEffect(() => {
    if (initialData) {
      setForm({ ...initialData });
    } else {
      setForm({ number: nextTableNumber || '', seats: '', status: 'available', reservationTime: '' });
    }
  }, [initialData, open, nextTableNumber]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.number || !form.seats) return;
    onSubmit({
      ...form,
      number: Number(form.number),
      seats: Number(form.seats),
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{initialData ? 'Edit Meja' : 'Tambah Meja'}</DialogTitle>
          <DialogDescription>
            {initialData ? 'Ubah informasi meja yang sudah ada' : 'Tambahkan meja baru ke dalam sistem'}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="number">Nomor Meja</Label>
            <Input
              id="number"
              name="number"
              type="number"
              value={form.number}
              onChange={handleChange}
              required
              readOnly={!initialData}
            />
          </div>
          <div>
            <Label htmlFor="seats">Jumlah Kursi</Label>
            <Input
              id="seats"
              name="seats"
              type="number"
              value={form.seats}
              onChange={handleChange}
              required
            />
          </div>
          <div>
            <Label htmlFor="status">Status</Label>
            <select
              id="status"
              name="status"
              value={form.status}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            >
              <option value="available">Tersedia</option>
              <option value="occupied">Terisi</option>
              <option value="reserved">Reservasi</option>
            </select>
          </div>
          {form.status === 'reserved' && (
            <div>
              <Label htmlFor="reservationTime">Waktu Reservasi</Label>
              <Input
                id="reservationTime"
                name="reservationTime"
                type="time"
                value={form.reservationTime}
                onChange={handleChange}
              />
            </div>
          )}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>Batal</Button>
            <Button type="submit">{initialData ? 'Simpan' : 'Tambah'}</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default TableFormModal; 