
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CreditCard, Banknote, Smartphone, Receipt, List, Clock, CheckCircle, XCircle } from 'lucide-react';
import { Order } from '../types/pos';
import { formatCurrency } from '../utils/posData';
import { useToast } from '../hooks/use-toast';
import Navigation from '../components/Navigation';

const Cashier = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'qr'>('cash');
  const [cashReceived, setCashReceived] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'payment' | 'orders'>('payment');
  const navigate = useNavigate();
  const { toast } = useToast();

  const fetchOrders = () => {
    fetch('/api/orders')
      .then(res => res.json())
      .then(data => {
        setOrders(data.map((order: any) => ({
          ...order,
          createdAt: new Date(order.createdAt),
          updatedAt: new Date(order.updatedAt)
        })));
      });
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  const handlePayment = async () => {
    if (!selectedOrder) return;

    const receivedAmount = parseFloat(cashReceived);
    if (paymentMethod === 'cash' && receivedAmount < selectedOrder.total) {
      toast({
        title: "Pembayaran tidak cukup",
        description: "Jumlah uang yang diterima kurang dari total tagihan",
        variant: "destructive"
      });
      return;
    }

    // Update order status to completed in the database
    try {
      await fetch(`/api/orders/${selectedOrder.id}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'completed' })
      });
      toast({
        title: "Pembayaran berhasil!",
        description: `Pesanan meja ${selectedOrder.tableId} telah dibayar`,
      });
      fetchOrders();
      setSelectedOrder(null);
      setCashReceived('');
      // Redirect ke halaman struk
      navigate(`/receipt/${selectedOrder.id}`);
    } catch (err) {
      toast({
        title: 'Gagal update status pembayaran',
        description: String(err),
        variant: 'destructive'
      });
    }
  };

  const getChange = () => {
    if (!selectedOrder || paymentMethod !== 'cash') return 0;
    const received = parseFloat(cashReceived) || 0;
    return Math.max(0, received - selectedOrder.total);
  };

  const readyToPayOrders = orders.filter(order => order.status === 'served');

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'preparing': return <Clock className="w-4 h-4 text-blue-500" />;
      case 'ready': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'served': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-gray-500" />;
      case 'cancelled': return <XCircle className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Menunggu';
      case 'preparing': return 'Dimasak';
      case 'ready': return 'Siap';
      case 'served': return 'Disajikan';
      case 'completed': return 'Selesai';
      case 'cancelled': return 'Dibatalkan';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'preparing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ready': return 'bg-green-100 text-green-800 border-green-200';
      case 'served': return 'bg-green-100 text-green-800 border-green-200';
      case 'completed': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <div className="container mx-auto px-6 py-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Kasir</h1>
          <p className="text-gray-600">Proses pembayaran dan cetak struk</p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('payment')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'payment'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  Pesanan Siap Bayar
                </div>
              </button>
              <button
                onClick={() => setActiveTab('orders')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'orders'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <List className="w-4 h-4" />
                  Daftar Pesanan
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'payment' ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Order List */}
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-6 border-b">
                <h3 className="text-xl font-semibold text-gray-800">Pesanan Siap Bayar</h3>
              </div>
              <div className="p-6 space-y-4">
                {readyToPayOrders.map(order => (
                  <div
                    key={order.id}
                    onClick={() => setSelectedOrder(order)}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedOrder?.id === order.id
                        ? 'border-primary bg-primary/5'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium">Meja {order.tableId}</h4>
                        <p className="text-sm text-gray-600">
                          {(order.orderItems || []).length} item
                        </p>
                      </div>
                      <span className="font-bold text-lg text-primary">
                        {formatCurrency(order.total)}
                      </span>
                    </div>

                    <div className="space-y-1">
                      {(order.orderItems || []).map(item => (
                        <div key={item.id} className="flex justify-between text-sm">
                          <span>{item.quantity}x {item.menuItem.name}</span>
                          <span>{formatCurrency(item.menuItem.price * item.quantity)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}

                {readyToPayOrders.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Receipt className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Tidak ada pesanan yang siap untuk dibayar</p>
                  </div>
                )}
              </div>
            </div>

          {/* Payment Section */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold text-gray-800">Pembayaran</h3>
            </div>
            <div className="p-6">
              {selectedOrder ? (
                <div className="space-y-6">
                  {/* Order Summary */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium mb-2">Meja {selectedOrder.tableId}</h4>
                    <div className="space-y-1">
                      {(selectedOrder.orderItems || []).map(item => (
                        <div key={item.id} className="flex justify-between text-sm">
                          <span>{item.quantity}x {item.menuItem.name}</span>
                          <span>{formatCurrency(item.menuItem.price * item.quantity)}</span>
                        </div>
                      ))}
                    </div>
                    <div className="border-t pt-2 mt-2">
                      <div className="flex justify-between font-bold">
                        <span>Total:</span>
                        <span>{formatCurrency(selectedOrder.total)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div>
                    <h4 className="font-medium mb-3">Metode Pembayaran</h4>
                    <div className="grid grid-cols-3 gap-3">
                      <button
                        onClick={() => setPaymentMethod('cash')}
                        className={`p-3 rounded-lg border-2 transition-all ${
                          paymentMethod === 'cash'
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Banknote className="w-6 h-6 mx-auto mb-1" />
                        <span className="text-sm">Tunai</span>
                      </button>
                      <button
                        onClick={() => setPaymentMethod('card')}
                        className={`p-3 rounded-lg border-2 transition-all ${
                          paymentMethod === 'card'
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <CreditCard className="w-6 h-6 mx-auto mb-1" />
                        <span className="text-sm">Kartu</span>
                      </button>
                      <button
                        onClick={() => setPaymentMethod('qr')}
                        className={`p-3 rounded-lg border-2 transition-all ${
                          paymentMethod === 'qr'
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Smartphone className="w-6 h-6 mx-auto mb-1" />
                        <span className="text-sm">QR Code</span>
                      </button>
                    </div>
                  </div>

                  {/* Cash Input */}
                  {paymentMethod === 'cash' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Uang Diterima
                      </label>
                      <input
                        type="number"
                        value={cashReceived}
                        onChange={(e) => setCashReceived(e.target.value)}
                        placeholder="Masukkan jumlah uang"
                        className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                      {cashReceived && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <div className="flex justify-between">
                            <span>Kembalian:</span>
                            <span className="font-bold">{formatCurrency(getChange())}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Payment Button */}
                  <button
                    onClick={handlePayment}
                    disabled={paymentMethod === 'cash' && (!cashReceived || parseFloat(cashReceived) < selectedOrder.total)}
                    className="w-full bg-primary text-primary-foreground py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Proses Pembayaran
                  </button>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <CreditCard className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Pilih pesanan untuk memulai pembayaran</p>
                </div>
              )}
            </div>
          </div>
        </div>
        ) : (
          /* Daftar Pesanan Tab */
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold text-gray-800">Daftar Semua Pesanan</h3>
              <p className="text-sm text-gray-600 mt-1">Lihat semua pesanan dengan status terkini</p>
            </div>
            <div className="p-6">
              {orders.length > 0 ? (
                <div className="space-y-4">
                  {orders.map(order => (
                    <div
                      key={order.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center gap-3">
                          <div>
                            <h4 className="font-medium">Meja {order.tableId}</h4>
                            <p className="text-sm text-gray-600">
                              {(order.orderItems || []).length} item • {new Date(order.createdAt).toLocaleString('id-ID')}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="font-bold text-lg">
                            {formatCurrency(order.total)}
                          </span>
                          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                            {getStatusIcon(order.status)}
                            {getStatusText(order.status)}
                          </div>
                        </div>
                      </div>

                      <div className="space-y-1">
                        {(order.orderItems || []).map(item => (
                          <div key={item.id} className="flex justify-between text-sm text-gray-600">
                            <span>{item.quantity}x {item.menuItem.name}</span>
                            <span>{formatCurrency(item.menuItem.price * item.quantity)}</span>
                          </div>
                        ))}
                      </div>

                      {/* Action buttons for specific statuses */}
                      {order.status === 'served' && (
                        <div className="mt-3 pt-3 border-t">
                          <button
                            onClick={() => {
                              setSelectedOrder(order);
                              setActiveTab('payment');
                            }}
                            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary/90 transition-colors"
                          >
                            Proses Pembayaran
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <List className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Belum ada pesanan</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Cashier;
